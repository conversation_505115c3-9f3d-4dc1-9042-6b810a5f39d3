app {
    name = "purchase-api-test"
    dns = "purchase-api-test"
    database {
      driverClassName="org.h2.Driver"
      maxPoolSize=1
      
      hostname="localhost"
      port=5432
      user="test"
      password="test"
      
      schema="public"
      database_default="test_db"
    }
    cybersource {
      merchant_id = "test_merchant_id"
      organization_id = "test_organization_id"
      run_environment = "sandbox"
      flex_environment = "sandbox"
      profiler_url = "https://h.online-metrix.net/fp/tags.js?org_id=test&session_id=test"
      profile_id = "test_profile_id"
      key_id = "test_key_id"
      key_secret = "test_key_secret"
      micro_form_origin = "http://localhost:8080"
    }
    apple {
      issuer_id = "test_issuer_id"
      key_id = "test_key_id"
      bundle_id = "com.test.app"
      app_apple_id = "test_app_id"
      private_key = "test_private_key"
      cas = "test_cas"
      apple_pay_merchant_identifier = "test_merchant_identifier"
      apple_pay_key_path = "test_key_path"
      apple_pay_certificate_path = "test_certificate_path"
    }
    google-play {
      resource_url = "https://test.googleapis.com"
      service_account_email = "<EMAIL>"
      application_name = "test-app"
    }
    paypal {
      client_id = "test_client_id"
      client_secret = "test_client_secret"
    }
}

ktor {
    deployment {
        hostname = "localhost"
        port = 8080
    }
    application {
        modules = [ ApplicationKt.module ]
    }
}
