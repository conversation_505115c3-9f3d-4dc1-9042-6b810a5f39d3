package fixtures

import models.*
import java.math.BigDecimal
import java.util.*
import kotlin.time.Clock

/**
 * Test data fixtures for consistent test data across all tests
 */
object TestDataFixtures {

    // Customer Test Data
    object Customers {
        const val VALID_CUSTOMER_ID = "test-customer-123"
        const val VALID_CUSTOMER_EMAIL = "<EMAIL>"
        const val VALID_CUSTOMER_PHONE = "+1234567890"
        const val VALID_FIRST_NAME = "John"
        const val VALID_LAST_NAME = "Doe"
        const val VALID_PROCESSOR_ID = "cybersource-customer-token-123"

        fun validCustomerDetail() = Request.CustomerDetail(
            id = VALID_CUSTOMER_ID,
            email = VALID_CUSTOMER_EMAIL,
            phone = VALID_CUSTOMER_PHONE,
            firstName = VALID_FIRST_NAME,
            lastName = VALID_LAST_NAME
        )

        fun validCustomer() = Customer(
            id = VALID_CUSTOMER_ID,
            email = VALID_CUSTOMER_EMAIL,
            billingAddress = Addresses.validAddress(),
            paymentInstruments = listOf(PaymentInstruments.validPaymentInstrument())
        )
    }

    // Address Test Data
    object Addresses {
        const val VALID_ISO2 = "US"
        const val VALID_STATE = "CA"
        const val VALID_CITY = "San Francisco"
        const val VALID_ZIP_CODE = "94105"
        const val VALID_STREET_ADDRESS = "123 Main St"
        const val VALID_EXTENDED_ADDRESS = "Apt 4B"

        fun validBillingAddress() = BillingAddress(
            firstName = Customers.VALID_FIRST_NAME,
            lastName = Customers.VALID_LAST_NAME,
            iso2 = VALID_ISO2,
            state = VALID_STATE,
            city = VALID_CITY,
            zipCode = VALID_ZIP_CODE,
            streetAddress = VALID_STREET_ADDRESS,
            extendedAddress = VALID_EXTENDED_ADDRESS
        )

        fun validAddress() = Address(
            iso2 = VALID_ISO2,
            state = VALID_STATE,
            city = VALID_CITY,
            zipCode = VALID_ZIP_CODE,
            streetAddress = VALID_STREET_ADDRESS,
            extendedAddress = VALID_EXTENDED_ADDRESS
        )
    }

    // Payment Test Data
    object Payments {
        const val VALID_PAYMENT_ID = "test-payment-123"
        const val VALID_AMOUNT = "100.00"
        const val VALID_CURRENCY = "USD"
        const val VALID_AUTHORIZATION_ID = "auth-ref-123"
        const val VALID_CAPTURE_ID = "capture-ref-123"
        const val VALID_REVERSAL_ID = "reversal-ref-123"
        const val VALID_VOID_ID = "void-ref-123"

        fun validPayment() = Payment(
            paymentId = VALID_PAYMENT_ID,
            customerId = Customers.VALID_CUSTOMER_ID,
            customerTokenId = Customers.VALID_PROCESSOR_ID,
            amount = VALID_AMOUNT,
            currency = VALID_CURRENCY,
            paymentStatus = PaymentStatus.PENDING,
            merchantStatus = null,
            paymentMethod = PaymentMethod.CREDIT_CARD,
            createdAt = Clock.System.now(),
            updatedAt = Clock.System.now(),
            canonicalName = CreditCards.VISA_SUCCESS_CANONICAL,
            cardType = CreditCards.VISA_BRAND,
            cardPrefix = CreditCards.VISA_PREFIX,
            cardSuffix = CreditCards.VISA_SUFFIX,
            expMonth = CreditCards.VALID_EXP_MONTH,
            expYear = CreditCards.VALID_EXP_YEAR,
            paymentInstrumentId = PaymentInstruments.VALID_PAYMENT_INSTRUMENT_ID,
            instrumentIdentifierId = null,
            deviceFingerprint = null
        )

        fun validPaymentEvent() = PaymentEvent(
            transactionId = UUID.randomUUID().toString(),
            referenceId = VALID_AUTHORIZATION_ID,
            paymentId = VALID_PAYMENT_ID,
            paymentEventStatus = PaymentEventStatus.SUCCESS,
            paymentEventType = PaymentEventType.AUTHORIZATION,
            amount = VALID_AMOUNT,
            currency = VALID_CURRENCY,
            errorCode = null,
            errorMessage = null,
            metadata = null,
            createdAt = Clock.System.now()
        )
    }

    // Credit Card Test Data
    object CreditCards {
        // Visa Test Cards
        const val VISA_SUCCESS = "****************"
        const val VISA_DECLINED = "****************"
        const val VISA_SUCCESS_CANONICAL = "****************"
        const val VISA_BRAND = "Visa"
        const val VISA_PREFIX = "411111"
        const val VISA_SUFFIX = "1111"

        // Mastercard Test Cards
        const val MASTERCARD_SUCCESS = "****************"
        const val MASTERCARD_DECLINED = "5000000000000009"
        const val MASTERCARD_BRAND = "Mastercard"

        // American Express Test Cards
        const val AMEX_SUCCESS = "***************"
        const val AMEX_DECLINED = "***************"
        const val AMEX_BRAND = "American Express"

        // Common Test Data
        const val VALID_EXP_MONTH = "12"
        const val VALID_EXP_YEAR = "2025"
        const val VALID_CVV = "123"
        const val VALID_AMEX_CVV = "1234"

        fun validCreditCardDetail() = Request.CreditCardDetail(
            paymentData = "mock-jwt-token-from-frontend",
            storeInVault = true
        )
    }

    // Payment Instrument Test Data
    object PaymentInstruments {
        const val VALID_PAYMENT_INSTRUMENT_ID = "payment-instrument-123"
        const val VALID_INSTRUMENT_IDENTIFIER_ID = "instrument-identifier-123"

        fun validPaymentInstrument() = PaymentInstrument(
            id = VALID_PAYMENT_INSTRUMENT_ID,
            paymentMethod = PaymentMethod.CREDIT_CARD.value,
            canonicalName = CreditCards.VISA_SUCCESS_CANONICAL,
            brand = CreditCards.VISA_BRAND,
            last4 = CreditCards.VISA_SUFFIX,
            isDefault = true
        )

        fun validVaultDetails() = Request.VaultDetails(
            paymentInstrumentId = VALID_PAYMENT_INSTRUMENT_ID
        )
    }

    // Device Fingerprint Test Data
    object DeviceFingerprints {
        const val VALID_SESSION_ID = "device-session-123"
        const val VALID_IP_ADDRESS = "***********"
        const val VALID_HOSTNAME = "customer-device.local"
        const val VALID_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

        fun validDeviceFingerprint() = DeviceFingerprint(
            sessionId = VALID_SESSION_ID,
            ipAddress = VALID_IP_ADDRESS,
            hostname = VALID_HOSTNAME,
            userAgent = VALID_USER_AGENT
        )
    }

    // Payment Session Test Data
    object PaymentSessions {
        const val VALID_PAYMENT_SESSION_ID = "payment-session-123"
        const val VALID_PAYMENT_SIGNATURE = "mock-jwt-signature"
        const val VALID_MERCHANT_ID = "test-merchant-id"
        const val VALID_ORGANIZATION_ID = "test-org-id"

        fun validPaymentSessionRequest() = Request.PaymentSession(
            paymentMethod = PaymentMethod.CREDIT_CARD.name,
            deviceType = DeviceType.WEB.name
        )

        fun validPaymentSession() = Response.PaymentSession(
            paymentId = VALID_PAYMENT_SESSION_ID,
            paymentSignature = VALID_PAYMENT_SIGNATURE,
            deviceType = DeviceType.WEB.name,
            paymentMethod = PaymentMethod.CREDIT_CARD.name,
            organizationId = VALID_ORGANIZATION_ID,
            merchantId = VALID_MERCHANT_ID
        )
    }

    // Authorization Request Test Data
    object AuthorizeRequests {
        fun validCreditCardAuthorizeRequest() = Request.Authorize(
            amount = Payments.VALID_AMOUNT,
            currency = Payments.VALID_CURRENCY,
            customerDetail = Customers.validCustomerDetail(),
            creditCardDetail = CreditCards.validCreditCardDetail(),
            vaultDetails = null,
            billingAddress = Addresses.validBillingAddress(),
            deviceFingerprint = DeviceFingerprints.validDeviceFingerprint()
        )

        fun validVaultAuthorizeRequest() = Request.Authorize(
            amount = Payments.VALID_AMOUNT,
            currency = Payments.VALID_CURRENCY,
            customerDetail = Customers.validCustomerDetail(),
            creditCardDetail = null,
            vaultDetails = PaymentInstruments.validVaultDetails(),
            billingAddress = null,
            deviceFingerprint = DeviceFingerprints.validDeviceFingerprint()
        )

        fun validDefaultVaultAuthorizeRequest() = Request.Authorize(
            amount = Payments.VALID_AMOUNT,
            currency = Payments.VALID_CURRENCY,
            customerDetail = Customers.validCustomerDetail(),
            creditCardDetail = null,
            vaultDetails = null,
            billingAddress = null,
            deviceFingerprint = DeviceFingerprints.validDeviceFingerprint()
        )
    }

    // Response Test Data
    object Responses {
        fun validAuthorizedResponse() = Response.Authorized(
            date = "2024-01-15T10:30:00Z",
            paymentId = Payments.VALID_PAYMENT_ID,
            amount = Payments.VALID_AMOUNT,
            currency = Payments.VALID_CURRENCY,
            canonicalName = CreditCards.VISA_SUCCESS_CANONICAL,
            cardType = CreditCards.VISA_BRAND
        )

        fun validCapturedResponse() = Response.Captured(
            date = "2024-01-15T10:35:00Z",
            paymentId = Payments.VALID_PAYMENT_ID,
            amount = Payments.VALID_AMOUNT,
            currency = Payments.VALID_CURRENCY,
            canonicalName = CreditCards.VISA_SUCCESS_CANONICAL,
            cardType = CreditCards.VISA_BRAND
        )

        fun validReversedResponse() = Response.Reversed(
            date = "2024-01-15T10:40:00Z",
            paymentId = Payments.VALID_PAYMENT_ID,
            amount = Payments.VALID_AMOUNT,
            currency = Payments.VALID_CURRENCY
        )

        fun validVoidedResponse() = Response.Voided(
            date = "2024-01-15T10:45:00Z",
            paymentId = Payments.VALID_PAYMENT_ID,
            amount = Payments.VALID_AMOUNT,
            currency = Payments.VALID_CURRENCY
        )
    }

    // Error Test Data
    object Errors {
        fun invalidCardError() = AppError(
            error = "INVALID_CARD",
            message = "Card number is invalid"
        )

        fun customerNotFoundError() = AppError(
            error = "CUSTOMER_NOT_FOUND",
            message = "Customer does not exist"
        )

        fun paymentNotFoundError() = AppError(
            error = "PAYMENT_NOT_FOUND",
            message = "Payment not found"
        )

        fun duplicateTransactionError() = AppError(
            error = "DUPLICATE_REQUEST",
            message = "Transaction already processed"
        )
    }
}
