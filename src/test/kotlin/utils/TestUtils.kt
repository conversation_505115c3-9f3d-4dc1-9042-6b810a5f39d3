package utils

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import fixtures.TestDataFixtures
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.testing.*
import kotlinx.serialization.json.Json
import models.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

/**
 * Test utilities for common test operations
 */
object TestUtils {

    private val json = Json { ignoreUnknownKeys = true }
    private val mapper = jacksonObjectMapper()

    /**
     * HTTP Test Utilities
     */
    object Http {
        
        /**
         * Makes a POST request and returns the response
         */
        suspend fun ApplicationTestBuilder.postRequest(
            url: String,
            body: Any? = null,
            headers: Map<String, String> = emptyMap()
        ): HttpResponse {
            return client.post(url) {
                contentType(ContentType.Application.Json)
                headers.forEach { (key, value) ->
                    header(key, value)
                }
                body?.let { setBody(it) }
            }
        }

        /**
         * Makes a GET request and returns the response
         */
        suspend fun ApplicationTestBuilder.getRequest(
            url: String,
            headers: Map<String, String> = emptyMap()
        ): HttpResponse {
            return client.get(url) {
                headers.forEach { (key, value) ->
                    header(key, value)
                }
            }
        }

        /**
         * Makes a PUT request and returns the response
         */
        suspend fun ApplicationTestBuilder.putRequest(
            url: String,
            body: Any? = null,
            headers: Map<String, String> = emptyMap()
        ): HttpResponse {
            return client.put(url) {
                contentType(ContentType.Application.Json)
                headers.forEach { (key, value) ->
                    header(key, value)
                }
                body?.let { setBody(it) }
            }
        }

        /**
         * Makes a PATCH request and returns the response
         */
        suspend fun ApplicationTestBuilder.patchRequest(
            url: String,
            body: Any? = null,
            headers: Map<String, String> = emptyMap()
        ): HttpResponse {
            return client.patch(url) {
                contentType(ContentType.Application.Json)
                headers.forEach { (key, value) ->
                    header(key, value)
                }
                body?.let { setBody(it) }
            }
        }

        /**
         * Makes a DELETE request and returns the response
         */
        suspend fun ApplicationTestBuilder.deleteRequest(
            url: String,
            headers: Map<String, String> = emptyMap()
        ): HttpResponse {
            return client.delete(url) {
                headers.forEach { (key, value) ->
                    header(key, value)
                }
            }
        }
    }

    /**
     * Response Assertion Utilities
     */
    object Assertions {

        /**
         * Asserts that the response has the expected status code
         */
        fun assertResponseStatus(response: HttpResponse, expectedStatus: HttpStatusCode) {
            assertEquals(expectedStatus, response.status, "Response status should be $expectedStatus")
        }

        /**
         * Asserts that the response body can be parsed as the expected type
         */
        suspend inline fun <reified T> assertResponseBody(response: HttpResponse): T {
            val bodyText = response.bodyAsText()
            assertNotNull(bodyText, "Response body should not be null")
            
            return try {
                mapper.readValue<T>(bodyText)
            } catch (e: Exception) {
                throw AssertionError("Failed to parse response body as ${T::class.simpleName}: $bodyText", e)
            }
        }

        /**
         * Asserts that the response contains an error with the expected code
         */
        suspend fun assertErrorResponse(response: HttpResponse, expectedErrorCode: String) {
            val error = assertResponseBody<AppError>(response)
            assertEquals(expectedErrorCode, error.error, "Error code should be $expectedErrorCode")
        }

        /**
         * Asserts that a payment session response is valid
         */
        fun assertValidPaymentSession(session: Response.PaymentSession) {
            assertNotNull(session.paymentId, "Payment ID should not be null")
            assertNotNull(session.paymentSignature, "Payment signature should not be null")
            assertNotNull(session.deviceType, "Device type should not be null")
            assertNotNull(session.paymentMethod, "Payment method should not be null")
            assertNotNull(session.organizationId, "Organization ID should not be null")
            assertNotNull(session.merchantId, "Merchant ID should not be null")
        }

        /**
         * Asserts that an authorization response is valid
         */
        fun assertValidAuthorizationResponse(auth: Response.Authorized) {
            assertNotNull(auth.paymentId, "Payment ID should not be null")
            assertNotNull(auth.date, "Date should not be null")
            assertNotNull(auth.amount, "Amount should not be null")
            assertNotNull(auth.currency, "Currency should not be null")
            assertNotNull(auth.canonicalName, "Canonical name should not be null")
            assertNotNull(auth.cardType, "Card type should not be null")
        }

        /**
         * Asserts that a capture response is valid
         */
        fun assertValidCaptureResponse(capture: Response.Captured) {
            assertNotNull(capture.paymentId, "Payment ID should not be null")
            assertNotNull(capture.date, "Date should not be null")
            assertNotNull(capture.amount, "Amount should not be null")
            assertNotNull(capture.currency, "Currency should not be null")
            assertNotNull(capture.canonicalName, "Canonical name should not be null")
            assertNotNull(capture.cardType, "Card type should not be null")
        }

        /**
         * Asserts that a customer response is valid
         */
        fun assertValidCustomerResponse(customer: Customer) {
            assertNotNull(customer.id, "Customer ID should not be null")
            assertNotNull(customer.email, "Customer email should not be null")
            assertNotNull(customer.paymentInstruments, "Payment instruments should not be null")
        }

        /**
         * Asserts that a payment instrument is valid
         */
        fun assertValidPaymentInstrument(instrument: PaymentInstrument) {
            assertNotNull(instrument.id, "Payment instrument ID should not be null")
            assertNotNull(instrument.paymentMethod, "Payment method should not be null")
            assertNotNull(instrument.canonicalName, "Canonical name should not be null")
            assertNotNull(instrument.brand, "Brand should not be null")
            assertNotNull(instrument.last4, "Last 4 digits should not be null")
        }
    }

    /**
     * Data Validation Utilities
     */
    object Validation {

        /**
         * Validates that a payment ID is in the correct format
         */
        fun isValidPaymentId(paymentId: String): Boolean {
            return paymentId.isNotBlank() && paymentId.length >= 10
        }

        /**
         * Validates that an amount is in the correct format
         */
        fun isValidAmount(amount: String): Boolean {
            return try {
                val decimal = amount.toBigDecimal()
                decimal > java.math.BigDecimal.ZERO
            } catch (e: NumberFormatException) {
                false
            }
        }

        /**
         * Validates that a currency code is valid
         */
        fun isValidCurrency(currency: String): Boolean {
            return currency.length == 3 && currency.all { it.isLetter() }
        }

        /**
         * Validates that an email address is in the correct format
         */
        fun isValidEmail(email: String): Boolean {
            return email.contains("@") && email.contains(".")
        }

        /**
         * Validates that a credit card number is in the correct format
         */
        fun isValidCardNumber(cardNumber: String): Boolean {
            return cardNumber.length in 13..19 && cardNumber.all { it.isDigit() }
        }
    }

    /**
     * Test Data Generation Utilities
     */
    object DataGeneration {

        /**
         * Generates a random payment ID
         */
        fun generatePaymentId(): String {
            return "test-payment-${System.currentTimeMillis()}"
        }

        /**
         * Generates a random customer ID
         */
        fun generateCustomerId(): String {
            return "test-customer-${System.currentTimeMillis()}"
        }

        /**
         * Generates a random email address
         */
        fun generateEmail(): String {
            return "test-${System.currentTimeMillis()}@example.com"
        }

        /**
         * Generates test credit card details for different scenarios
         */
        fun generateCreditCardDetail(scenario: TestScenario = TestScenario.SUCCESS): Request.CreditCardDetail {
            return when (scenario) {
                TestScenario.SUCCESS -> Request.CreditCardDetail(
                    paymentData = "mock-jwt-success-token",
                    storeInVault = true
                )
                TestScenario.DECLINED -> Request.CreditCardDetail(
                    paymentData = "mock-jwt-declined-token",
                    storeInVault = false
                )
                TestScenario.ERROR -> Request.CreditCardDetail(
                    paymentData = "mock-jwt-error-token",
                    storeInVault = false
                )
            }
        }

        /**
         * Generates test billing address with optional invalid fields
         */
        fun generateBillingAddress(valid: Boolean = true): BillingAddress {
            return if (valid) {
                TestDataFixtures.Addresses.validBillingAddress()
            } else {
                BillingAddress(
                    firstName = "",
                    lastName = "",
                    iso2 = "XX",
                    state = "",
                    city = "",
                    zipCode = "",
                    streetAddress = "",
                    extendedAddress = ""
                )
            }
        }
    }

    /**
     * Test scenarios for different test cases
     */
    enum class TestScenario {
        SUCCESS,
        DECLINED,
        ERROR
    }

    /**
     * Common test constants
     */
    object Constants {
        const val API_BASE_PATH = "/api/v1"
        const val PAYMENT_SESSIONS_PATH = "$API_BASE_PATH/payment-sessions"
        const val PAYMENTS_PATH = "$API_BASE_PATH/payments"
        const val CUSTOMERS_PATH = "$API_BASE_PATH/customers"
        
        const val TEST_TIMEOUT_MS = 5000L
        const val DEFAULT_TEST_AMOUNT = "100.00"
        const val DEFAULT_TEST_CURRENCY = "USD"
    }

    /**
     * Database test utilities
     */
    object Database {
        
        /**
         * Cleans up test data from the database
         */
        fun cleanupTestData() {
            // Implementation would depend on your database setup
            // This is a placeholder for database cleanup operations
        }

        /**
         * Sets up test data in the database
         */
        fun setupTestData() {
            // Implementation would depend on your database setup
            // This is a placeholder for database setup operations
        }
    }

    /**
     * Logging utilities for tests
     */
    object Logging {
        
        /**
         * Logs test information
         */
        fun logTestInfo(message: String) {
            println("[TEST INFO] $message")
        }

        /**
         * Logs test errors
         */
        fun logTestError(message: String, throwable: Throwable? = null) {
            println("[TEST ERROR] $message")
            throwable?.printStackTrace()
        }

        /**
         * Logs HTTP request/response for debugging
         */
        fun logHttpExchange(method: String, url: String, status: HttpStatusCode, body: String? = null) {
            println("[HTTP] $method $url -> $status")
            body?.let { println("[HTTP BODY] $it") }
        }
    }
}
