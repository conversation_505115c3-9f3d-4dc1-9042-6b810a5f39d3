package utils

import Api.*
import Invokers.ApiClient
import Invokers.ApiException
import Model.*
import fixtures.TestDataFixtures
import io.mockk.*
import models.*
import repositories.CustomerRepository
import repositories.PaymentRepository
import services.CybersourceService
import usecases.CustomerUseCase
import utils.CybersourceUtils
import java.math.BigDecimal
import kotlin.time.Clock

/**
 * Mock utilities for testing
 */
object MockUtils {

    /**
     * Creates a mock CybersourceService with common behaviors
     */
    fun createMockCybersourceService(): CybersourceService {
        val mockPaymentRepository = mockk<PaymentRepository>()
        val mockService = mockk<CybersourceService>()

        // Mock payment session generation
        every { mockService.generateSession(any(), any()) } returns TestDataFixtures.PaymentSessions.validPaymentSession()

        // Mock payment session retrieval
        every { mockService.getPaymentSession(any()) } returns TestDataFixtures.PaymentSessions.validPaymentSession()

        // Mock customer operations
        every { mockService.createCustomer(any(), any()) } returns TestDataFixtures.Customers.VALID_PROCESSOR_ID
        every { mockService.getCustomer(any()) } returns TestDataFixtures.Customers.validCustomer()
        every { mockService.deleteCustomer(any()) } just Runs

        // Mock payment instrument operations
        every { mockService.updatePaymentInstrument(any(), any(), any()) } returns listOf(TestDataFixtures.PaymentInstruments.validPaymentInstrument())
        every { mockService.updateDefaultPaymentInstrument(any(), any(), any()) } returns listOf(TestDataFixtures.PaymentInstruments.validPaymentInstrument())
        every { mockService.deletePaymentInstrument(any(), any()) } returns listOf(TestDataFixtures.PaymentInstruments.validPaymentInstrument())

        // Mock payment operations
        every { 
            mockService.authorizeWithCreditCard(
                any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any()
            ) 
        } returns TestDataFixtures.Responses.validAuthorizedResponse()

        every { mockService.captureAuthorization(any()) } returns TestDataFixtures.Responses.validCapturedResponse()
        every { mockService.reverseAuthorization(any(), any()) } returns TestDataFixtures.Responses.validReversedResponse()
        every { mockService.voidCapture(any()) } returns TestDataFixtures.Responses.validVoidedResponse()

        return mockService
    }

    /**
     * Creates a mock CustomerRepository with common behaviors
     */
    fun createMockCustomerRepository(): CustomerRepository {
        val mockRepository = mockk<CustomerRepository>()

        every { mockRepository.findCustomerById(any()) } returns createMockCustomerEntity()
        every { mockRepository.deleteCustomerById(any()) } just Runs

        return mockRepository
    }

    /**
     * Creates a mock PaymentRepository with common behaviors
     */
    fun createMockPaymentRepository(): PaymentRepository {
        val mockRepository = mockk<PaymentRepository>()

        every { mockRepository.addPayment(any(), any(), any(), any(), any(), any()) } just Runs
        every { mockRepository.findPaymentById(any()) } returns TestDataFixtures.Payments.validPayment()
        every { mockRepository.updatePayment(any(), any(), any()) } just Runs
        every { mockRepository.updatePaymentAuthorization(any(), any(), any(), any(), any(), any(), any(), any(), any()) } just Runs
        every { mockRepository.addPaymentEvent(any(), any(), any(), any(), any(), any(), any()) } just Runs
        every { mockRepository.addPaymentEventAuthorization(any(), any(), any(), any(), any(), any(), any(), any(), any(), any()) } just Runs
        every { mockRepository.findPaymentEventsByPaymentId(any()) } returns listOf(TestDataFixtures.Payments.validPaymentEvent())
        every { mockRepository.fetchAllPaymentsWithEvents() } returns listOf(createMockPaymentWithEvents())

        return mockRepository
    }

    /**
     * Creates a mock CustomerUseCase with common behaviors
     */
    fun createMockCustomerUseCase(): CustomerUseCase {
        val mockUseCase = mockk<CustomerUseCase>()

        every { mockUseCase.getCustomerTokenId(any(), any()) } returns TestDataFixtures.Customers.VALID_PROCESSOR_ID

        return mockUseCase
    }

    /**
     * Creates a mock ApiClient for Cybersource API testing
     */
    fun createMockApiClient(): ApiClient {
        val mockClient = mockk<ApiClient>()
        
        // Mock merchant config
        every { mockClient.merchantConfig = any() } just Runs
        every { mockClient.merchantConfig } returns mockk()

        return mockClient
    }

    /**
     * Creates mock Cybersource API instances
     */
    object CybersourceApiMocks {
        
        fun createMockCustomerApi(): CustomerApi {
            val mockApi = mockk<CustomerApi>()

            // Mock customer creation
            every { mockApi.postCustomer(any(), any()) } returns createMockPostCustomerResponse()

            // Mock customer retrieval
            every { mockApi.getCustomer(any(), any()) } returns createMockGetCustomerResponse()

            // Mock customer deletion
            every { mockApi.deleteCustomer(any(), any()) } just Runs

            // Mock customer update
            every { mockApi.patchCustomer(any(), any(), any(), any()) } returns createMockPatchCustomerResponse()

            return mockApi
        }

        fun createMockPaymentsApi(): PaymentsApi {
            val mockApi = mockk<PaymentsApi>()

            // Mock payment authorization
            every { mockApi.createPayment(any()) } returns createMockCreatePaymentResponse()

            return mockApi
        }

        fun createMockCaptureApi(): CaptureApi {
            val mockApi = mockk<CaptureApi>()

            // Mock payment capture
            every { mockApi.capturePayment(any(), any()) } returns createMockCapturePaymentResponse()

            return mockApi
        }

        fun createMockReversalApi(): ReversalApi {
            val mockApi = mockk<ReversalApi>()

            // Mock payment reversal
            every { mockApi.authReversal(any(), any()) } returns createMockAuthReversalResponse()

            return mockApi
        }

        fun createMockVoidApi(): VoidApi {
            val mockApi = mockk<VoidApi>()

            // Mock payment void
            every { mockApi.voidPayment(any(), any()) } returns createMockVoidPaymentResponse()

            return mockApi
        }

        fun createMockPaymentInstrumentApi(): PaymentInstrumentApi {
            val mockApi = mockk<PaymentInstrumentApi>()

            // Mock payment instrument update
            every { mockApi.patchPaymentInstrument(any(), any(), any(), any(), any()) } returns createMockPatchPaymentInstrumentResponse()

            return mockApi
        }

        fun createMockCustomerPaymentInstrumentApi(): CustomerPaymentInstrumentApi {
            val mockApi = mockk<CustomerPaymentInstrumentApi>()

            // Mock payment instrument deletion
            every { mockApi.deleteCustomerPaymentInstrument(any(), any(), any()) } just Runs

            return mockApi
        }

        fun createMockMicroformIntegrationApi(): MicroformIntegrationApi {
            val mockApi = mockk<MicroformIntegrationApi>()

            // Mock signature generation
            every { mockApi.generateCaptureContext(any()) } returns TestDataFixtures.PaymentSessions.VALID_PAYMENT_SIGNATURE

            return mockApi
        }
    }

    /**
     * Creates mock Cybersource response objects
     */
    private fun createMockPostCustomerResponse(): PostCustomerResponse {
        val response = mockk<PostCustomerResponse>()
        every { response.id } returns TestDataFixtures.Customers.VALID_PROCESSOR_ID
        return response
    }

    private fun createMockGetCustomerResponse(): GetCustomerResponse {
        val response = mockk<GetCustomerResponse>()
        val buyerInfo = mockk<Tmsv2customersBuyerInformation>()
        val embedded = mockk<Tmsv2customersEmbedded>()
        val defaultPaymentInstrument = mockk<Tmsv2customersEmbeddedDefaultPaymentInstrument>()
        val billTo = mockk<Tmsv2customersEmbeddedDefaultPaymentInstrumentBillTo>()

        every { response.id } returns TestDataFixtures.Customers.VALID_PROCESSOR_ID
        every { response.buyerInformation } returns buyerInfo
        every { response.embedded } returns embedded
        every { buyerInfo.email } returns TestDataFixtures.Customers.VALID_CUSTOMER_EMAIL
        every { buyerInfo.merchantCustomerID } returns TestDataFixtures.Customers.VALID_CUSTOMER_ID
        every { embedded.defaultPaymentInstrument } returns defaultPaymentInstrument
        every { defaultPaymentInstrument.billTo } returns billTo
        every { billTo.country } returns TestDataFixtures.Addresses.VALID_ISO2
        every { billTo.administrativeArea } returns TestDataFixtures.Addresses.VALID_STATE
        every { billTo.locality } returns TestDataFixtures.Addresses.VALID_CITY
        every { billTo.postalCode } returns TestDataFixtures.Addresses.VALID_ZIP_CODE
        every { billTo.address1 } returns TestDataFixtures.Addresses.VALID_STREET_ADDRESS
        every { billTo.address2 } returns TestDataFixtures.Addresses.VALID_EXTENDED_ADDRESS

        return response
    }

    private fun createMockPatchCustomerResponse(): PatchCustomerResponse {
        val response = mockk<PatchCustomerResponse>()
        every { response.id } returns TestDataFixtures.Customers.VALID_PROCESSOR_ID
        return response
    }

    private fun createMockCreatePaymentResponse(): PtsV2PaymentsPost201Response {
        val response = mockk<PtsV2PaymentsPost201Response>()
        val orderInfo = mockk<PtsV2PaymentsPost201ResponseOrderInformation>()
        val amountDetails = mockk<PtsV2PaymentsPost201ResponseOrderInformationAmountDetails>()

        every { response.id } returns TestDataFixtures.Payments.VALID_AUTHORIZATION_ID
        every { response.status } returns "AUTHORIZED"
        every { response.submitTimeUtc } returns "2024-01-15T10:30:00Z"
        every { response.orderInformation } returns orderInfo
        every { orderInfo.amountDetails } returns amountDetails
        every { amountDetails.authorizedAmount } returns TestDataFixtures.Payments.VALID_AMOUNT
        every { amountDetails.currency } returns TestDataFixtures.Payments.VALID_CURRENCY

        return response
    }

    private fun createMockCapturePaymentResponse(): PtsV2PaymentsCapturesPost201Response {
        val response = mockk<PtsV2PaymentsCapturesPost201Response>()
        val orderInfo = mockk<PtsV2PaymentsCapturesPost201ResponseOrderInformation>()
        val amountDetails = mockk<PtsV2PaymentsCapturesPost201ResponseOrderInformationAmountDetails>()

        every { response.id } returns TestDataFixtures.Payments.VALID_CAPTURE_ID
        every { response.status } returns "PENDING"
        every { response.submitTimeUtc } returns "2024-01-15T10:35:00Z"
        every { response.orderInformation } returns orderInfo
        every { orderInfo.amountDetails } returns amountDetails
        every { amountDetails.totalAmount } returns TestDataFixtures.Payments.VALID_AMOUNT
        every { amountDetails.currency } returns TestDataFixtures.Payments.VALID_CURRENCY

        return response
    }

    private fun createMockAuthReversalResponse(): PtsV2PaymentsReversalsPost201Response {
        val response = mockk<PtsV2PaymentsReversalsPost201Response>()
        val amountDetails = mockk<PtsV2PaymentsReversalsPost201ResponseReversalAmountDetails>()

        every { response.id } returns TestDataFixtures.Payments.VALID_REVERSAL_ID
        every { response.status } returns "REVERSED"
        every { response.submitTimeUtc } returns "2024-01-15T10:40:00Z"
        every { response.reversalAmountDetails } returns amountDetails
        every { amountDetails.reversedAmount } returns TestDataFixtures.Payments.VALID_AMOUNT
        every { amountDetails.currency } returns TestDataFixtures.Payments.VALID_CURRENCY

        return response
    }

    private fun createMockVoidPaymentResponse(): PtsV2PaymentsVoidsPost201Response {
        val response = mockk<PtsV2PaymentsVoidsPost201Response>()
        val amountDetails = mockk<PtsV2PaymentsVoidsPost201ResponseVoidAmountDetails>()

        every { response.id } returns TestDataFixtures.Payments.VALID_VOID_ID
        every { response.status } returns "VOIDED"
        every { response.submitTimeUtc } returns "2024-01-15T10:45:00Z"
        every { response.voidAmountDetails } returns amountDetails
        every { amountDetails.voidAmount } returns TestDataFixtures.Payments.VALID_AMOUNT
        every { amountDetails.currency } returns TestDataFixtures.Payments.VALID_CURRENCY

        return response
    }

    private fun createMockPatchPaymentInstrumentResponse(): PatchPaymentInstrumentResponse {
        val response = mockk<PatchPaymentInstrumentResponse>()
        val billTo = mockk<Tmsv2customersEmbeddedDefaultPaymentInstrumentBillTo>()

        every { response.billTo } returns billTo
        every { billTo.firstName } returns TestDataFixtures.Customers.VALID_FIRST_NAME
        every { billTo.lastName } returns TestDataFixtures.Customers.VALID_LAST_NAME

        return response
    }

    /**
     * Helper methods for creating mock entities
     */
    private fun createMockCustomerEntity(): models.Customer {
        return TestDataFixtures.Customers.validCustomer()
    }

    private fun createMockPaymentWithEvents(): repositories.PaymentWithEvents {
        return repositories.PaymentWithEvents(
            payment = TestDataFixtures.Payments.validPayment(),
            events = listOf(TestDataFixtures.Payments.validPaymentEvent())
        )
    }

    /**
     * Creates mock ApiException for error testing
     */
    fun createMockApiException(code: Int, responseBody: String): ApiException {
        return ApiException(code, responseBody)
    }

    /**
     * Mock CybersourceUtils for testing
     */
    fun mockCybersourceUtils() {
        mockkObject(CybersourceUtils)
        every { CybersourceUtils.getMerchantConfig() } returns mockk()
        every { CybersourceUtils.generateSignatureMicroForm() } returns TestDataFixtures.PaymentSessions.VALID_PAYMENT_SIGNATURE
        every { CybersourceUtils.verifyJwtAndGetDecodedBody(any()) } returns mockk()
        every { CybersourceUtils.PROFILE_ID } returns "test-profile-id"
        every { CybersourceUtils.MERCHANT_ID } returns TestDataFixtures.PaymentSessions.VALID_MERCHANT_ID
        every { CybersourceUtils.ORGANIZATION_ID } returns TestDataFixtures.PaymentSessions.VALID_ORGANIZATION_ID
    }
}
