package routes

import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*
import module

/**
 * Integration tests for Customer routes following Ktor testing guidelines
 */
class CustomerRoutesTest {

    @Nested
    @DisplayName("GET /api/v1/customers/{id}")
    inner class GetCustomerTests {

        @Test
        @DisplayName("Should return 404 for non-existent customer")
        fun testGetNonExistentCustomer() = testApplication {
            application {
                module()
            }

            // When
            val response = client.get("/api/v1/customers/non-existent-customer")

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should handle empty customer ID")
        fun testGetCustomerWithEmptyId() = testApplication {
            application {
                module()
            }

            // When
            val response = client.get("/api/v1/customers/")

            // Then
            // Should return 404 or 405 depending on routing configuration
            assertTrue(
                response.status == HttpStatusCode.NotFound || 
                response.status == HttpStatusCode.MethodNotAllowed
            )
        }

        @Test
        @DisplayName("Should handle special characters in customer ID")
        fun testGetCustomerWithSpecialCharacters() = testApplication {
            application {
                module()
            }

            // When
            val response = client.get("/api/v1/customers/customer-with-special-chars-@#$%")

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should handle very long customer ID")
        fun testGetCustomerWithLongId() = testApplication {
            application {
                module()
            }

            // Given - Very long customer ID
            val longId = "a".repeat(1000)

            // When
            val response = client.get("/api/v1/customers/$longId")

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }
    }

    @Nested
    @DisplayName("DELETE /api/v1/customers/{id}")
    inner class DeleteCustomerTests {

        @Test
        @DisplayName("Should return 404 for non-existent customer")
        fun testDeleteNonExistentCustomer() = testApplication {
            application {
                module()
            }

            // When
            val response = client.delete("/api/v1/customers/non-existent-customer")

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should handle empty customer ID in delete")
        fun testDeleteCustomerWithEmptyId() = testApplication {
            application {
                module()
            }

            // When
            val response = client.delete("/api/v1/customers/")

            // Then
            assertTrue(
                response.status == HttpStatusCode.NotFound || 
                response.status == HttpStatusCode.MethodNotAllowed
            )
        }
    }

    @Nested
    @DisplayName("PUT /api/v1/customers/{id}/payment-instruments/{instrumentId}")
    inner class UpdatePaymentInstrumentTests {

        @Test
        @DisplayName("Should return 404 for non-existent customer")
        fun testUpdatePaymentInstrumentNonExistentCustomer() = testApplication {
            application {
                module()
            }

            // Given
            val requestBody = """
                {
                    "billingAddress": {
                        "firstName": "John",
                        "lastName": "Doe",
                        "streetAddress": "123 Main St",
                        "city": "New York",
                        "state": "NY",
                        "zipCode": "10001",
                        "iso2": "US"
                    }
                }
            """.trimIndent()

            // When
            val response = client.put("/api/v1/customers/non-existent-customer/payment-instruments/instrument-123") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should return 400 for invalid billing address")
        fun testUpdatePaymentInstrumentInvalidAddress() = testApplication {
            application {
                module()
            }

            // Given - Invalid request body
            val requestBody = """
                {
                    "invalidField": "invalid value"
                }
            """.trimIndent()

            // When
            val response = client.put("/api/v1/customers/customer-123/payment-instruments/instrument-123") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }

        @Test
        @DisplayName("Should return 400 for malformed JSON")
        fun testUpdatePaymentInstrumentMalformedJson() = testApplication {
            application {
                module()
            }

            // When
            val response = client.put("/api/v1/customers/customer-123/payment-instruments/instrument-123") {
                contentType(ContentType.Application.Json)
                setBody("invalid json")
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }

    @Nested
    @DisplayName("PATCH /api/v1/customers/{id}/payment-instruments/{instrumentId}")
    inner class UpdateDefaultPaymentInstrumentTests {

        @Test
        @DisplayName("Should return 404 for non-existent customer")
        fun testUpdateDefaultPaymentInstrumentNonExistentCustomer() = testApplication {
            application {
                module()
            }

            // Given
            val requestBody = """
                {
                    "isDefault": true
                }
            """.trimIndent()

            // When
            val response = client.patch("/api/v1/customers/non-existent-customer/payment-instruments/instrument-123") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should return 400 for invalid default flag")
        fun testUpdateDefaultPaymentInstrumentInvalidFlag() = testApplication {
            application {
                module()
            }

            // Given - Invalid boolean value
            val requestBody = """
                {
                    "isDefault": "not-a-boolean"
                }
            """.trimIndent()

            // When
            val response = client.patch("/api/v1/customers/customer-123/payment-instruments/instrument-123") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }

        @Test
        @DisplayName("Should handle missing isDefault field")
        fun testUpdateDefaultPaymentInstrumentMissingField() = testApplication {
            application {
                module()
            }

            // Given - Missing isDefault field
            val requestBody = """
                {
                    "someOtherField": "value"
                }
            """.trimIndent()

            // When
            val response = client.patch("/api/v1/customers/customer-123/payment-instruments/instrument-123") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }

    @Nested
    @DisplayName("DELETE /api/v1/customers/{id}/payment-instruments/{instrumentId}")
    inner class DeletePaymentInstrumentTests {

        @Test
        @DisplayName("Should return 404 for non-existent customer")
        fun testDeletePaymentInstrumentNonExistentCustomer() = testApplication {
            application {
                module()
            }

            // When
            val response = client.delete("/api/v1/customers/non-existent-customer/payment-instruments/instrument-123")

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should return 404 for non-existent payment instrument")
        fun testDeleteNonExistentPaymentInstrument() = testApplication {
            application {
                module()
            }

            // When
            val response = client.delete("/api/v1/customers/customer-123/payment-instruments/non-existent-instrument")

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should handle empty payment instrument ID")
        fun testDeletePaymentInstrumentEmptyId() = testApplication {
            application {
                module()
            }

            // When
            val response = client.delete("/api/v1/customers/customer-123/payment-instruments/")

            // Then
            assertTrue(
                response.status == HttpStatusCode.NotFound || 
                response.status == HttpStatusCode.MethodNotAllowed
            )
        }
    }

    @Nested
    @DisplayName("HTTP Method Tests")
    inner class HttpMethodTests {

        @Test
        @DisplayName("Should return 405 for unsupported methods on customers endpoint")
        fun testUnsupportedMethodsOnCustomers() = testApplication {
            application {
                module()
            }

            // Test POST method on customer endpoint (not supported)
            val postResponse = client.post("/api/v1/customers/customer-123")
            assertEquals(HttpStatusCode.MethodNotAllowed, postResponse.status)

            // Test PUT method on customer endpoint (not supported)
            val putResponse = client.put("/api/v1/customers/customer-123")
            assertEquals(HttpStatusCode.MethodNotAllowed, putResponse.status)

            // Test PATCH method on customer endpoint (not supported)
            val patchResponse = client.patch("/api/v1/customers/customer-123")
            assertEquals(HttpStatusCode.MethodNotAllowed, patchResponse.status)
        }

        @Test
        @DisplayName("Should return 405 for unsupported methods on payment instruments")
        fun testUnsupportedMethodsOnPaymentInstruments() = testApplication {
            application {
                module()
            }

            // Test GET method on payment instrument endpoint (not supported)
            val getResponse = client.get("/api/v1/customers/customer-123/payment-instruments/instrument-123")
            assertEquals(HttpStatusCode.MethodNotAllowed, getResponse.status)

            // Test POST method on payment instrument endpoint (not supported)
            val postResponse = client.post("/api/v1/customers/customer-123/payment-instruments/instrument-123")
            assertEquals(HttpStatusCode.MethodNotAllowed, postResponse.status)
        }
    }

    @Nested
    @DisplayName("Content Type Tests")
    inner class ContentTypeTests {

        @Test
        @DisplayName("Should require JSON content type for PUT requests")
        fun testPutRequiresJsonContentType() = testApplication {
            application {
                module()
            }

            // When - Send request without JSON content type
            val response = client.put("/api/v1/customers/customer-123/payment-instruments/instrument-123") {
                contentType(ContentType.Text.Plain)
                setBody("not json")
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }

        @Test
        @DisplayName("Should require JSON content type for PATCH requests")
        fun testPatchRequiresJsonContentType() = testApplication {
            application {
                module()
            }

            // When - Send request without JSON content type
            val response = client.patch("/api/v1/customers/customer-123/payment-instruments/instrument-123") {
                contentType(ContentType.Text.Plain)
                setBody("not json")
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }
}
