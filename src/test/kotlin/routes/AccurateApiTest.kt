package routes

import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*
import kotlin.test.assertContains
import module

/**
 * Comprehensive API tests based on actual implementation and README documentation
 * Tests all 13 endpoints with correct request/response formats
 */
class AccurateApiTest {

    @Nested
    @DisplayName("Payment Sessions - POST /api/v1/payment-sessions")
    inner class PaymentSessionTests {

        @Test
        @DisplayName("Should create CREDIT_CARD payment session successfully")
        fun testCreateCreditCardSession() = testApplication {
            application { module() }

            val response = client.post("/api/v1/payment-sessions") {
                contentType(ContentType.Application.Json)
                setBody("""{"paymentMethod": "CREDIT_CARD", "deviceType": "WEB"}""")
            }

            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            assertContains(body, "paymentId")
            assertContains(body, "paymentSignature")
            assertContains(body, "merchantId")
            assertContains(body, "organizationId")
        }

        @Test
        @DisplayName("Should validate payment method enum values")
        fun testPaymentMethodValidation() = testApplication {
            application { module() }

            // Test invalid payment method
            val response = client.post("/api/v1/payment-sessions") {
                contentType(ContentType.Application.Json)
                setBody("""{"paymentMethod": "INVALID_METHOD", "deviceType": "WEB"}""")
            }

            assertEquals(HttpStatusCode.BadRequest, response.status)
        }

        @Test
        @DisplayName("Should validate device type enum values")
        fun testDeviceTypeValidation() = testApplication {
            application { module() }

            // Test invalid device type
            val response = client.post("/api/v1/payment-sessions") {
                contentType(ContentType.Application.Json)
                setBody("""{"paymentMethod": "CREDIT_CARD", "deviceType": "INVALID_DEVICE"}""")
            }

            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }

    @Nested
    @DisplayName("Payment Authorization - POST /api/v1/payments/{id}/authorize")
    inner class PaymentAuthorizationTests {

        @Test
        @DisplayName("Should require valid payment session for authorization")
        fun testAuthorizationRequiresValidSession() = testApplication {
            application { module() }

            val authRequest = """
                {
                    "amount": "100.00",
                    "currency": "USD",
                    "customerDetail": {
                        "id": "customer_123",
                        "email": "<EMAIL>",
                        "phone": "+1234567890"
                    },
                    "creditCardDetail": {
                        "paymentData": "test_jwt_token",
                        "storeInVault": true
                    },
                    "billingAddress": {
                        "firstName": "John",
                        "lastName": "Doe",
                        "streetAddress": "123 Main St",
                        "city": "New York",
                        "state": "NY",
                        "zipCode": "10001",
                        "iso2": "US"
                    }
                }
            """.trimIndent()

            val response = client.post("/api/v1/payments/non-existent-payment/authorize") {
                contentType(ContentType.Application.Json)
                setBody(authRequest)
            }

            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should validate required fields for credit card authorization")
        fun testCreditCardAuthorizationValidation() = testApplication {
            application { module() }

            // Missing required fields
            val invalidRequest = """
                {
                    "amount": "100.00",
                    "currency": "USD"
                }
            """.trimIndent()

            val response = client.post("/api/v1/payments/test-payment-id/authorize") {
                contentType(ContentType.Application.Json)
                setBody(invalidRequest)
            }

            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }

    @Nested
    @DisplayName("Payment Operations")
    inner class PaymentOperationsTests {

        @Test
        @DisplayName("POST /api/v1/payments/{id}/capture - Should require valid payment")
        fun testCaptureRequiresValidPayment() = testApplication {
            application { module() }

            val response = client.post("/api/v1/payments/non-existent-payment/capture") {
                contentType(ContentType.Application.Json)
                setBody("""{"paymentId": "non-existent-payment"}""")
            }

            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("POST /api/v1/payments/{id}/reverse - Should require reason")
        fun testReverseRequiresReason() = testApplication {
            application { module() }

            val response = client.post("/api/v1/payments/test-payment/reverse") {
                contentType(ContentType.Application.Json)
                setBody("""{"paymentId": "test-payment"}""")
            }

            // Should return 400 for missing reason or 404 for non-existent payment
            assertTrue(response.status == HttpStatusCode.BadRequest || response.status == HttpStatusCode.NotFound)
        }

        @Test
        @DisplayName("POST /api/v1/payments/{id}/void - Should require valid payment")
        fun testVoidRequiresValidPayment() = testApplication {
            application { module() }

            val response = client.post("/api/v1/payments/non-existent-payment/void") {
                contentType(ContentType.Application.Json)
                setBody("""{"paymentId": "non-existent-payment"}""")
            }

            assertEquals(HttpStatusCode.NotFound, response.status)
        }
    }

    @Nested
    @DisplayName("Payment Inquiry")
    inner class PaymentInquiryTests {

        @Test
        @DisplayName("GET /api/v1/payments - Should return payments list")
        fun testGetAllPayments() = testApplication {
            application { module() }

            val response = client.get("/api/v1/payments")

            assertEquals(HttpStatusCode.OK, response.status)
            assertEquals(ContentType.Application.Json, response.contentType()?.withoutParameters())
            
            val body = response.bodyAsText()
            assertTrue(body.startsWith("[") && body.endsWith("]"))
        }

        @Test
        @DisplayName("GET /api/v1/payments/{id} - Should return 404 for non-existent payment")
        fun testGetPaymentById() = testApplication {
            application { module() }

            val response = client.get("/api/v1/payments/non-existent-payment")

            assertEquals(HttpStatusCode.NotFound, response.status)
        }
    }

    @Nested
    @DisplayName("Customer Management")
    inner class CustomerManagementTests {

        @Test
        @DisplayName("GET /api/v1/customers/{id} - Should return 404 for non-existent customer")
        fun testGetCustomer() = testApplication {
            application { module() }

            val response = client.get("/api/v1/customers/non-existent-customer")

            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("DELETE /api/v1/customers/{id} - Should return 404 for non-existent customer")
        fun testDeleteCustomer() = testApplication {
            application { module() }

            val response = client.delete("/api/v1/customers/non-existent-customer")

            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("PUT /api/v1/customers/{id}/payment-instruments/{instrumentId} - Should validate billing address")
        fun testUpdatePaymentInstrument() = testApplication {
            application { module() }

            val updateRequest = """
                {
                    "billingAddress": {
                        "firstName": "John",
                        "lastName": "Doe",
                        "streetAddress": "456 Oak Ave",
                        "city": "Los Angeles",
                        "state": "CA",
                        "zipCode": "90210",
                        "iso2": "US"
                    }
                }
            """.trimIndent()

            val response = client.put("/api/v1/customers/test-customer/payment-instruments/test-instrument") {
                contentType(ContentType.Application.Json)
                setBody(updateRequest)
            }

            // Should return 404 for non-existent customer/instrument
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("PATCH /api/v1/customers/{id}/payment-instruments/{instrumentId} - Should validate isDefault field")
        fun testUpdateDefaultPaymentInstrument() = testApplication {
            application { module() }

            val response = client.patch("/api/v1/customers/test-customer/payment-instruments/test-instrument") {
                contentType(ContentType.Application.Json)
                setBody("""{"isDefault": true}""")
            }

            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("DELETE /api/v1/customers/{id}/payment-instruments/{instrumentId} - Should return 404 for non-existent")
        fun testDeletePaymentInstrument() = testApplication {
            application { module() }

            val response = client.delete("/api/v1/customers/test-customer/payment-instruments/test-instrument")

            assertEquals(HttpStatusCode.NotFound, response.status)
        }
    }

    @Nested
    @DisplayName("Health Check")
    inner class HealthCheckTests {

        @Test
        @DisplayName("GET /healthz - Should return health status")
        fun testHealthCheck() = testApplication {
            application { module() }

            val response = client.get("/healthz")

            assertEquals(HttpStatusCode.OK, response.status)
        }
    }

    @Nested
    @DisplayName("Error Handling")
    inner class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle malformed JSON gracefully")
        fun testMalformedJson() = testApplication {
            application { module() }

            val response = client.post("/api/v1/payment-sessions") {
                contentType(ContentType.Application.Json)
                setBody("invalid json")
            }

            assertEquals(HttpStatusCode.BadRequest, response.status)
        }

        @Test
        @DisplayName("Should handle missing Content-Type header")
        fun testMissingContentType() = testApplication {
            application { module() }

            val response = client.post("/api/v1/payment-sessions") {
                setBody("""{"paymentMethod": "CREDIT_CARD", "deviceType": "WEB"}""")
            }

            // Should still work or return appropriate error
            assertTrue(response.status == HttpStatusCode.OK || response.status == HttpStatusCode.BadRequest)
        }

        @Test
        @DisplayName("Should handle empty request body")
        fun testEmptyRequestBody() = testApplication {
            application { module() }

            val response = client.post("/api/v1/payment-sessions") {
                contentType(ContentType.Application.Json)
                setBody("{}")
            }

            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }
}
