package routes

import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*
import kotlin.test.assertContains
import module

/**
 * Integration tests for Payment routes following Ktor testing guidelines
 */
class PaymentRoutesTest {

    @Nested
    @DisplayName("GET /api/v1/payments")
    inner class GetAllPaymentsTests {

        @Test
        @DisplayName("Should return all payments successfully")
        fun testGetAllPayments() = testApplication {
            application {
                module()
            }

            // When
            val response = client.get("/api/v1/payments")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            
            val responseBody = response.bodyAsText()
            // Should return an array (even if empty)
            assertTrue(responseBody.startsWith("["))
            assertTrue(responseBody.endsWith("]"))
        }

        @Test
        @DisplayName("Should return JSON content type")
        fun testGetAllPaymentsContentType() = testApplication {
            application {
                module()
            }

            // When
            val response = client.get("/api/v1/payments")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            assertEquals(ContentType.Application.Json, response.contentType()?.withoutParameters())
        }
    }

    @Nested
    @DisplayName("GET /api/v1/payments/{id}")
    inner class GetPaymentByIdTests {

        @Test
        @DisplayName("Should return 404 for non-existent payment")
        fun testGetNonExistentPayment() = testApplication {
            application {
                module()
            }

            // When
            val response = client.get("/api/v1/payments/non-existent-payment-id")

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should handle empty payment ID")
        fun testGetPaymentWithEmptyId() = testApplication {
            application {
                module()
            }

            // When
            val response = client.get("/api/v1/payments/")

            // Then
            // Should return 404 or 405 depending on routing configuration
            assertTrue(
                response.status == HttpStatusCode.NotFound || 
                response.status == HttpStatusCode.MethodNotAllowed
            )
        }

        @Test
        @DisplayName("Should handle special characters in payment ID")
        fun testGetPaymentWithSpecialCharacters() = testApplication {
            application {
                module()
            }

            // When
            val response = client.get("/api/v1/payments/payment-with-special-chars-@#$%")

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }
    }

    @Nested
    @DisplayName("POST /api/v1/payments/{id}/authorize")
    inner class AuthorizePaymentTests {

        @Test
        @DisplayName("Should return 404 for non-existent payment session")
        fun testAuthorizeNonExistentPayment() = testApplication {
            application {
                module()
            }

            // Given
            val requestBody = """
                {
                    "amount": "100.00",
                    "currency": "USD",
                    "customerDetail": {
                        "id": "customer_123",
                        "email": "<EMAIL>"
                    },
                    "creditCardDetail": {
                        "paymentData": "test-jwt-token",
                        "storeInVault": true
                    },
                    "billingAddress": {
                        "firstName": "John",
                        "lastName": "Doe",
                        "streetAddress": "123 Main St",
                        "city": "New York",
                        "state": "NY",
                        "zipCode": "10001",
                        "iso2": "US"
                    }
                }
            """.trimIndent()

            // When
            val response = client.post("/api/v1/payments/non-existent-payment/authorize") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should return 400 for invalid authorization request")
        fun testAuthorizeWithInvalidRequest() = testApplication {
            application {
                module()
            }

            // Given - Missing required fields
            val requestBody = """
                {
                    "amount": "100.00"
                }
            """.trimIndent()

            // When
            val response = client.post("/api/v1/payments/some-payment-id/authorize") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }

        @Test
        @DisplayName("Should return 400 for malformed JSON in authorization")
        fun testAuthorizeWithMalformedJson() = testApplication {
            application {
                module()
            }

            // When
            val response = client.post("/api/v1/payments/some-payment-id/authorize") {
                contentType(ContentType.Application.Json)
                setBody("invalid json")
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }

    @Nested
    @DisplayName("POST /api/v1/payments/{id}/capture")
    inner class CapturePaymentTests {

        @Test
        @DisplayName("Should return 404 for non-existent payment")
        fun testCaptureNonExistentPayment() = testApplication {
            application {
                module()
            }

            // Given
            val requestBody = """
                {
                    "paymentId": "non-existent-payment"
                }
            """.trimIndent()

            // When
            val response = client.post("/api/v1/payments/non-existent-payment/capture") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should return 400 for invalid capture request")
        fun testCaptureWithInvalidRequest() = testApplication {
            application {
                module()
            }

            // Given - Empty request body
            val requestBody = "{}"

            // When
            val response = client.post("/api/v1/payments/some-payment-id/capture") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }

    @Nested
    @DisplayName("POST /api/v1/payments/{id}/reverse")
    inner class ReversePaymentTests {

        @Test
        @DisplayName("Should return 404 for non-existent payment")
        fun testReverseNonExistentPayment() = testApplication {
            application {
                module()
            }

            // Given
            val requestBody = """
                {
                    "paymentId": "non-existent-payment",
                    "reason": "Customer requested cancellation"
                }
            """.trimIndent()

            // When
            val response = client.post("/api/v1/payments/non-existent-payment/reverse") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should return 400 for missing reason")
        fun testReverseWithMissingReason() = testApplication {
            application {
                module()
            }

            // Given - Missing reason
            val requestBody = """
                {
                    "paymentId": "some-payment-id"
                }
            """.trimIndent()

            // When
            val response = client.post("/api/v1/payments/some-payment-id/reverse") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }

    @Nested
    @DisplayName("POST /api/v1/payments/{id}/void")
    inner class VoidPaymentTests {

        @Test
        @DisplayName("Should return 404 for non-existent payment")
        fun testVoidNonExistentPayment() = testApplication {
            application {
                module()
            }

            // Given
            val requestBody = """
                {
                    "paymentId": "non-existent-payment"
                }
            """.trimIndent()

            // When
            val response = client.post("/api/v1/payments/non-existent-payment/void") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.NotFound, response.status)
        }

        @Test
        @DisplayName("Should return 400 for invalid void request")
        fun testVoidWithInvalidRequest() = testApplication {
            application {
                module()
            }

            // Given - Empty request body
            val requestBody = "{}"

            // When
            val response = client.post("/api/v1/payments/some-payment-id/void") {
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
        }
    }

    @Nested
    @DisplayName("HTTP Method Tests")
    inner class HttpMethodTests {

        @Test
        @DisplayName("Should return 405 for unsupported methods on payments endpoint")
        fun testUnsupportedMethodsOnPayments() = testApplication {
            application {
                module()
            }

            // Test PUT method (not supported)
            val putResponse = client.put("/api/v1/payments")
            assertEquals(HttpStatusCode.MethodNotAllowed, putResponse.status)

            // Test DELETE method (not supported)
            val deleteResponse = client.delete("/api/v1/payments")
            assertEquals(HttpStatusCode.MethodNotAllowed, deleteResponse.status)

            // Test PATCH method (not supported)
            val patchResponse = client.patch("/api/v1/payments")
            assertEquals(HttpStatusCode.MethodNotAllowed, patchResponse.status)
        }
    }
}
