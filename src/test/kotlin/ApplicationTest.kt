import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import module

/**
 * Basic application test following Ktor testing guidelines
 * https://ktor.io/docs/server-testing.html
 */
class ApplicationTest {

    @Test
    fun testRoot() = testApplication {
        application {
            module()
        }
        
        client.get("/").apply {
            assertEquals(HttpStatusCode.NotFound, status)
        }
    }

    @Test
    fun testHealthEndpoint() = testApplication {
        application {
            module()
        }
        
        client.get("/healthz").apply {
            assertEquals(HttpStatusCode.OK, status)
        }
    }
}
