package services

import io.mockk.*
import models.*
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Assertions.assertAll
import repositories.PaymentRepository
import java.math.BigDecimal
import java.util.*
import kotlin.time.Instant

/**
 * Unit tests for CybersourceService following JUnit 5 guidelines
 * https://kotlinlang.org/docs/jvm-test-using-junit.html
 */
class CybersourceServiceTest {

    private lateinit var mockPaymentRepository: PaymentRepository
    private lateinit var cybersourceService: CybersourceService

    @BeforeEach
    fun setUp() {
        // Clear all mocks before each test
        clearAllMocks()
        
        // Create mock repository
        mockPaymentRepository = mockk<PaymentRepository>()
        
        // Create service instance with mocked repository
        cybersourceService = CybersourceService(mockPaymentRepository)
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("Payment Session Tests")
    inner class PaymentSessionTests {

        @Test
        @DisplayName("Should generate payment session successfully")
        fun testGenerateSessionSuccess() {
            // Given
            val deviceType = DeviceType.WEB.name
            val paymentMethod = PaymentMethod.CREDIT_CARD

            // When
            val result = cybersourceService.generateSession(deviceType, paymentMethod)

            // Then
            kotlin.test.assertNotNull(result)
            assertEquals(deviceType, result.deviceType)
            assertEquals(paymentMethod.name, result.paymentMethod)
            kotlin.test.assertNotNull(result.paymentId)
            kotlin.test.assertNotNull(result.paymentSignature)
            kotlin.test.assertNotNull(result.merchantId)
            kotlin.test.assertNotNull(result.organizationId)
        }

        @Test
        @DisplayName("Should generate unique payment IDs")
        fun testGenerateUniquePaymentIds() {
            // Given
            val deviceType = DeviceType.WEB.name
            val paymentMethod = PaymentMethod.CREDIT_CARD

            // When
            val session1 = cybersourceService.generateSession(deviceType, paymentMethod)
            val session2 = cybersourceService.generateSession(deviceType, paymentMethod)

            // Then
            assertNotEquals(session1.paymentId, session2.paymentId)
        }

        @Test
        @DisplayName("Should handle different device types")
        fun testDifferentDeviceTypes() {
            // Given
            val paymentMethod = PaymentMethod.CREDIT_CARD
            val deviceTypes = listOf(DeviceType.WEB, DeviceType.ANDROID, DeviceType.IOS)

            // When & Then
            deviceTypes.forEach { deviceType ->
                val result = cybersourceService.generateSession(deviceType.name, paymentMethod)
                assertEquals(deviceType.name, result.deviceType)
            }
        }

        @Test
        @DisplayName("Should handle different payment methods")
        fun testDifferentPaymentMethods() {
            // Given
            val deviceType = DeviceType.WEB.name
            val paymentMethods = listOf(
                PaymentMethod.CREDIT_CARD,
                PaymentMethod.VAULT,
                PaymentMethod.DEFAULT_VAULT
            )

            // When & Then
            paymentMethods.forEach { paymentMethod ->
                val result = cybersourceService.generateSession(deviceType, paymentMethod)
                assertEquals(paymentMethod.name, result.paymentMethod)
            }
        }
    }

    @Nested
    @DisplayName("Payment Repository Integration Tests")
    inner class PaymentRepositoryTests {

        @Test
        @DisplayName("Should call repository when adding payment")
        fun testAddPaymentCallsRepository() {
            // Given
            val paymentId = "test-payment-123"
            val customerId = "test-customer-123"
            val customerTokenId = "test-token-123"
            val amount = BigDecimal("100.00")
            val currency = "USD"
            val paymentMethod = PaymentMethod.CREDIT_CARD

            every { 
                mockPaymentRepository.addPayment(
                    paymentId, customerId, customerTokenId, amount, currency, paymentMethod
                ) 
            } returns Unit

            // When
            // This would be called internally by authorize methods
            mockPaymentRepository.addPayment(paymentId, customerId, customerTokenId, amount, currency, paymentMethod)

            // Then
            verify(exactly = 1) { 
                mockPaymentRepository.addPayment(
                    paymentId, customerId, customerTokenId, amount, currency, paymentMethod
                ) 
            }
        }

        @Test
        @DisplayName("Should call repository when finding payment")
        fun testFindPaymentCallsRepository() {
            // Given
            val paymentId = "test-payment-123"
            val mockPayment = Payment(
                paymentId = paymentId,
                customerId = "test-customer-123",
                customerTokenId = "test-token-123",
                amount = "100.00",
                currency = "USD",
                paymentStatus = PaymentStatus.PENDING,
                merchantStatus = "AUTHORIZED",
                paymentMethod = PaymentMethod.CREDIT_CARD,
                createdAt = Instant.fromEpochMilliseconds(System.currentTimeMillis()),
                updatedAt = Instant.fromEpochMilliseconds(System.currentTimeMillis()),
                canonicalName = "Test Customer",
                cardType = "VISA",
                cardPrefix = "4111",
                cardSuffix = "1111",
                expMonth = "12",
                expYear = "2025",
                paymentInstrumentId = "test-instrument-123",
                instrumentIdentifierId = "test-identifier-123",
                deviceFingerprint = "test-fingerprint"
            )

            every { mockPaymentRepository.findPaymentById(paymentId) } returns mockPayment

            // When
            val result = mockPaymentRepository.findPaymentById(paymentId)

            // Then
            verify(exactly = 1) { mockPaymentRepository.findPaymentById(paymentId) }
            assertEquals(mockPayment, result)
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    inner class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle null payment session gracefully")
        fun testNullPaymentSessionHandling() {
            // Given
            val nonExistentPaymentId = "non-existent-payment"

            // When & Then
            assertThrows<AppException.NotFoundException> {
                cybersourceService.getPaymentSession(nonExistentPaymentId)
            }
        }

        @Test
        @DisplayName("Should validate payment session parameters")
        fun testPaymentSessionParameterValidation() {
            // Test with null device type should not crash
            assertDoesNotThrow {
                cybersourceService.generateSession("", PaymentMethod.CREDIT_CARD)
            }
        }
    }

    @Nested
    @DisplayName("Business Logic Tests")
    inner class BusinessLogicTests {

        @Test
        @DisplayName("Should maintain payment session state")
        fun testPaymentSessionState() {
            // Given
            val deviceType = DeviceType.WEB.name
            val paymentMethod = PaymentMethod.CREDIT_CARD

            // When
            val session = cybersourceService.generateSession(deviceType, paymentMethod)

            // Then - Session should be retrievable
            assertDoesNotThrow {
                cybersourceService.getPaymentSession(session.paymentId)
            }
        }

        @Test
        @DisplayName("Should generate valid UUID for payment ID")
        fun testPaymentIdFormat() {
            // Given
            val deviceType = DeviceType.WEB.name
            val paymentMethod = PaymentMethod.CREDIT_CARD

            // When
            val session = cybersourceService.generateSession(deviceType, paymentMethod)

            // Then - Payment ID should be a valid UUID format
            assertDoesNotThrow {
                UUID.fromString(session.paymentId)
            }
        }

        @Test
        @DisplayName("Should include required fields in payment session")
        fun testPaymentSessionRequiredFields() {
            // Given
            val deviceType = DeviceType.WEB.name
            val paymentMethod = PaymentMethod.CREDIT_CARD

            // When
            val session = cybersourceService.generateSession(deviceType, paymentMethod)

            // Then - All required fields should be present
            assertAll(
                { kotlin.test.assertNotNull(session.paymentId, "Payment ID should not be null") },
                { kotlin.test.assertNotNull(session.paymentSignature, "Payment signature should not be null") },
                { kotlin.test.assertNotNull(session.deviceType, "Device type should not be null") },
                { kotlin.test.assertNotNull(session.paymentMethod, "Payment method should not be null") },
                { kotlin.test.assertNotNull(session.merchantId, "Merchant ID should not be null") },
                { kotlin.test.assertNotNull(session.organizationId, "Organization ID should not be null") },
                { assertTrue(session.paymentId.isNotBlank(), "Payment ID should not be blank") },
                { assertTrue(session.paymentSignature.isNotBlank(), "Payment signature should not be blank") },
                { assertTrue(session.deviceType.isNotBlank(), "Device type should not be blank") },
                { assertTrue(session.paymentMethod.isNotBlank(), "Payment method should not be blank") },
                { assertTrue(session.merchantId.isNotBlank(), "Merchant ID should not be blank") },
                { assertTrue(session.organizationId.isNotBlank(), "Organization ID should not be blank") }
            )
        }
    }

    @Nested
    @DisplayName("Performance Tests")
    inner class PerformanceTests {

        @Test
        @DisplayName("Should generate payment sessions quickly")
        fun testPaymentSessionGenerationPerformance() {
            // Given
            val deviceType = DeviceType.WEB.name
            val paymentMethod = PaymentMethod.CREDIT_CARD
            val iterations = 10

            // When
            val startTime = System.currentTimeMillis()
            repeat(iterations) {
                cybersourceService.generateSession(deviceType, paymentMethod)
            }
            val endTime = System.currentTimeMillis()

            // Then - Should complete within reasonable time (less than 5 seconds for 10 iterations)
            val totalTime = endTime - startTime
            assertTrue(totalTime < 5000, "Payment session generation took too long: ${totalTime}ms")
        }
    }
}
