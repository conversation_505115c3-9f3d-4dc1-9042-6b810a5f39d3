package services

import Api.*
import Invokers.ApiClient
import Invokers.ApiException
import Model.*
import config.TestConfig
import fixtures.TestDataFixtures
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.*
import models.*
import repositories.PaymentRepository
import utils.CybersourceUtils
import utils.MockUtils
import java.math.BigDecimal

/**
 * Unit tests for CybersourceService
 */
class CybersourceServiceUnitTest : DescribeSpec({

    // Test setup
    lateinit var mockPaymentRepository: PaymentRepository
    lateinit var mockApiClient: ApiClient
    lateinit var cybersourceService: CybersourceService

    beforeEach {
        // Clear all mocks before each test
        clearAllMocks()
        
        // Setup mocks
        MockUtils.mockCybersourceUtils()
        mockPaymentRepository = MockUtils.createMockPaymentRepository()
        mockApiClient = MockUtils.createMockApiClient()
        
        // Create service instance
        cybersourceService = CybersourceService(mockPaymentRepository)
        
        // Mock the API client in the service
        every { cybersourceService["apiClient"] } returns mockApiClient
    }

    afterEach {
        clearAllMocks()
    }

    describe("Customer Management") {
        
        describe("createCustomer") {
            it("should create customer successfully") {
                // Arrange
                val customerId = TestDataFixtures.Customers.VALID_CUSTOMER_ID
                val customerEmail = TestDataFixtures.Customers.VALID_CUSTOMER_EMAIL
                val expectedProcessorId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                
                val mockCustomerApi = MockUtils.CybersourceApiMocks.createMockCustomerApi()
                every { CustomerApi(mockApiClient) } returns mockCustomerApi

                // Act
                val result = cybersourceService.createCustomer(customerId, customerEmail)

                // Assert
                result shouldBe expectedProcessorId
                verify { mockCustomerApi.postCustomer(any(), any()) }
            }

            it("should throw ConflictException when customer already exists") {
                // Arrange
                val customerId = TestDataFixtures.Customers.VALID_CUSTOMER_ID
                val customerEmail = TestDataFixtures.Customers.VALID_CUSTOMER_EMAIL
                
                val mockCustomerApi = mockk<CustomerApi>()
                every { CustomerApi(mockApiClient) } returns mockCustomerApi
                every { mockCustomerApi.postCustomer(any(), any()) } throws ApiException(409, "Conflict")

                // Act & Assert
                val exception = shouldThrow<AppException.ConflictException> {
                    cybersourceService.createCustomer(customerId, customerEmail)
                }
                exception.message shouldBe "payment profile already exists for customer"
            }

            it("should throw InternalServerErrorException for server errors") {
                // Arrange
                val customerId = TestDataFixtures.Customers.VALID_CUSTOMER_ID
                val customerEmail = TestDataFixtures.Customers.VALID_CUSTOMER_EMAIL
                
                val mockCustomerApi = mockk<CustomerApi>()
                every { CustomerApi(mockApiClient) } returns mockCustomerApi
                every { mockCustomerApi.postCustomer(any(), any()) } throws ApiException(500, "Internal Server Error")

                // Act & Assert
                val exception = shouldThrow<AppException.InternalServerErrorException> {
                    cybersourceService.createCustomer(customerId, customerEmail)
                }
                exception.message shouldBe "Unexpected error"
            }
        }

        describe("getCustomer") {
            it("should retrieve customer successfully") {
                // Arrange
                val processorId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val mockCustomerApi = MockUtils.CybersourceApiMocks.createMockCustomerApi()
                every { CustomerApi(mockApiClient) } returns mockCustomerApi

                // Mock getPaymentInstruments method
                every { cybersourceService.getPaymentInstruments(any()) } returns listOf(
                    TestDataFixtures.PaymentInstruments.validPaymentInstrument()
                )

                // Act
                val result = cybersourceService.getCustomer(processorId)

                // Assert
                result shouldNotBe null
                result.id shouldBe TestDataFixtures.Customers.VALID_CUSTOMER_ID
                result.email shouldBe TestDataFixtures.Customers.VALID_CUSTOMER_EMAIL
                result.paymentInstruments.size shouldBe 1
                verify { mockCustomerApi.getCustomer(processorId, any()) }
            }

            it("should throw NotFoundException when customer not found") {
                // Arrange
                val processorId = "non-existent-customer"
                val mockCustomerApi = mockk<CustomerApi>()
                every { CustomerApi(mockApiClient) } returns mockCustomerApi
                every { mockCustomerApi.getCustomer(any(), any()) } throws ApiException(404, "Not Found")

                // Act & Assert
                val exception = shouldThrow<AppException.NotFoundException> {
                    cybersourceService.getCustomer(processorId)
                }
                exception.message shouldBe "Customer payment profile not found"
            }
        }

        describe("deleteCustomer") {
            it("should delete customer successfully") {
                // Arrange
                val processorId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val mockCustomerApi = MockUtils.CybersourceApiMocks.createMockCustomerApi()
                every { CustomerApi(mockApiClient) } returns mockCustomerApi

                // Act
                cybersourceService.deleteCustomer(processorId)

                // Assert
                verify { mockCustomerApi.deleteCustomer(processorId, any()) }
            }

            it("should throw NotFoundException when customer not found") {
                // Arrange
                val processorId = "non-existent-customer"
                val mockCustomerApi = mockk<CustomerApi>()
                every { CustomerApi(mockApiClient) } returns mockCustomerApi
                every { mockCustomerApi.deleteCustomer(any(), any()) } throws ApiException(404, "Not Found")

                // Act & Assert
                val exception = shouldThrow<AppException.NotFoundException> {
                    cybersourceService.deleteCustomer(processorId)
                }
                exception.message shouldBe "Customer payment profile not found"
            }
        }
    }

    describe("Payment Session Management") {
        
        describe("generateSession") {
            it("should generate payment session successfully") {
                // Arrange
                val deviceType = DeviceType.WEB.name
                val paymentMethod = PaymentMethod.CREDIT_CARD

                // Act
                val result = cybersourceService.generateSession(deviceType, paymentMethod)

                // Assert
                result shouldNotBe null
                result.deviceType shouldBe deviceType
                result.paymentMethod shouldBe paymentMethod.name
                result.paymentId shouldNotBe null
                result.paymentSignature shouldNotBe null
                result.merchantId shouldNotBe null
                result.organizationId shouldNotBe null
            }

            it("should throw InternalServerErrorException when signature generation fails") {
                // Arrange
                val deviceType = DeviceType.WEB.name
                val paymentMethod = PaymentMethod.CREDIT_CARD
                
                every { CybersourceUtils.generateSignatureMicroForm() } returns null

                // Act & Assert
                val exception = shouldThrow<AppException.InternalServerErrorException> {
                    cybersourceService.generateSession(deviceType, paymentMethod)
                }
                exception.message shouldBe "Unable to generate signature"
            }
        }

        describe("getPaymentSession") {
            it("should retrieve payment session successfully") {
                // Arrange
                val paymentId = TestDataFixtures.PaymentSessions.VALID_PAYMENT_SESSION_ID
                val expectedSession = TestDataFixtures.PaymentSessions.validPaymentSession()
                
                // First generate a session to store it
                every { CybersourceUtils.generateSignatureMicroForm() } returns expectedSession.paymentSignature
                cybersourceService.generateSession(expectedSession.deviceType, PaymentMethod.valueOf(expectedSession.paymentMethod))

                // Act
                val result = cybersourceService.getPaymentSession(paymentId)

                // Assert
                result shouldNotBe null
                result.paymentId shouldBe paymentId
            }

            it("should throw NotFoundException when session not found") {
                // Arrange
                val paymentId = "non-existent-session"

                // Act & Assert
                val exception = shouldThrow<AppException.NotFoundException> {
                    cybersourceService.getPaymentSession(paymentId)
                }
                exception.message shouldBe "Payment session not found"
            }
        }
    }

    describe("Payment Instrument Management") {
        
        describe("updateDefaultPaymentInstrument") {
            it("should update default payment instrument successfully") {
                // Arrange
                val customerTokenId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val paymentInstrumentId = TestDataFixtures.PaymentInstruments.VALID_PAYMENT_INSTRUMENT_ID
                val isDefault = true
                
                val mockCustomerApi = MockUtils.CybersourceApiMocks.createMockCustomerApi()
                every { CustomerApi(mockApiClient) } returns mockCustomerApi
                every { cybersourceService.getPaymentInstruments(any()) } returns listOf(
                    TestDataFixtures.PaymentInstruments.validPaymentInstrument()
                )

                // Act
                val result = cybersourceService.updateDefaultPaymentInstrument(
                    customerTokenId, paymentInstrumentId, isDefault
                )

                // Assert
                result shouldNotBe null
                result.size shouldBe 1
                verify { mockCustomerApi.patchCustomer(customerTokenId, any(), any(), any()) }
            }

            it("should throw NotFoundException when customer not found") {
                // Arrange
                val customerTokenId = "non-existent-customer"
                val paymentInstrumentId = TestDataFixtures.PaymentInstruments.VALID_PAYMENT_INSTRUMENT_ID
                val isDefault = true
                
                val mockCustomerApi = mockk<CustomerApi>()
                every { CustomerApi(mockApiClient) } returns mockCustomerApi
                every { mockCustomerApi.patchCustomer(any(), any(), any(), any()) } throws ApiException(404, "Not Found")

                // Act & Assert
                val exception = shouldThrow<AppException.NotFoundException> {
                    cybersourceService.updateDefaultPaymentInstrument(customerTokenId, paymentInstrumentId, isDefault)
                }
                exception.message shouldBe "Customer payment profile not found"
            }
        }

        describe("updatePaymentInstrument") {
            it("should update payment instrument successfully") {
                // Arrange
                val customerTokenId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val paymentInstrumentId = TestDataFixtures.PaymentInstruments.VALID_PAYMENT_INSTRUMENT_ID
                val billingAddress = TestDataFixtures.Addresses.validBillingAddress()
                
                val mockPaymentInstrumentApi = MockUtils.CybersourceApiMocks.createMockPaymentInstrumentApi()
                every { PaymentInstrumentApi(mockApiClient) } returns mockPaymentInstrumentApi
                every { cybersourceService.getPaymentInstruments(any()) } returns listOf(
                    TestDataFixtures.PaymentInstruments.validPaymentInstrument()
                )

                // Act
                val result = cybersourceService.updatePaymentInstrument(
                    customerTokenId, paymentInstrumentId, billingAddress
                )

                // Assert
                result shouldNotBe null
                result.size shouldBe 1
                verify { mockPaymentInstrumentApi.patchPaymentInstrument(paymentInstrumentId, any(), any(), any(), any()) }
            }
        }

        describe("deletePaymentInstrument") {
            it("should delete payment instrument successfully") {
                // Arrange
                val customerTokenId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val paymentInstrumentId = TestDataFixtures.PaymentInstruments.VALID_PAYMENT_INSTRUMENT_ID
                
                val mockCustomerPaymentInstrumentApi = MockUtils.CybersourceApiMocks.createMockCustomerPaymentInstrumentApi()
                every { CustomerPaymentInstrumentApi(mockApiClient) } returns mockCustomerPaymentInstrumentApi
                every { cybersourceService.getPaymentInstruments(any()) } returns listOf(
                    TestDataFixtures.PaymentInstruments.validPaymentInstrument()
                )

                // Act
                val result = cybersourceService.deletePaymentInstrument(customerTokenId, paymentInstrumentId)

                // Assert
                result shouldNotBe null
                verify { mockCustomerPaymentInstrumentApi.deleteCustomerPaymentInstrument(customerTokenId, paymentInstrumentId, any()) }
            }

            it("should throw ConflictException when trying to delete default payment instrument") {
                // Arrange
                val customerTokenId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val paymentInstrumentId = TestDataFixtures.PaymentInstruments.VALID_PAYMENT_INSTRUMENT_ID
                
                val mockCustomerPaymentInstrumentApi = mockk<CustomerPaymentInstrumentApi>()
                every { CustomerPaymentInstrumentApi(mockApiClient) } returns mockCustomerPaymentInstrumentApi
                every { mockCustomerPaymentInstrumentApi.deleteCustomerPaymentInstrument(any(), any(), any()) } throws ApiException(409, "Conflict")

                // Act & Assert
                val exception = shouldThrow<AppException.ConflictException> {
                    cybersourceService.deletePaymentInstrument(customerTokenId, paymentInstrumentId)
                }
                exception.message shouldBe "Cannot delete default payment instrument"
            }
        }
    }
})
