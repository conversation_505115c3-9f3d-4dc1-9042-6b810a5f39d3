package services

import Api.*
import Invokers.ApiClient
import Invokers.ApiException
import Model.*
import fixtures.TestDataFixtures
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.*
import models.*
import repositories.PaymentRepository
import utils.CybersourceUtils
import utils.MockUtils
import java.math.BigDecimal

/**
 * Unit tests for CybersourceService payment operations
 */
class CybersourceServicePaymentUnitTest : DescribeSpec({

    // Test setup
    lateinit var mockPaymentRepository: PaymentRepository
    lateinit var mockApiClient: ApiClient
    lateinit var cybersourceService: CybersourceService

    beforeEach {
        clearAllMocks()
        MockUtils.mockCybersourceUtils()
        mockPaymentRepository = MockUtils.createMockPaymentRepository()
        mockApiClient = MockUtils.createMockApiClient()
        cybersourceService = CybersourceService(mockPaymentRepository)
        every { cybersourceService["apiClient"] } returns mockApiClient
    }

    afterEach {
        clearAllMocks()
    }

    describe("Payment Authorization") {
        
        describe("authorizeWithCreditCard - Credit Card Payment") {
            it("should authorize credit card payment successfully") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val paymentData = "mock-jwt-token"
                val customerTokenId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val customerId = TestDataFixtures.Customers.VALID_CUSTOMER_ID
                val customerEmail = TestDataFixtures.Customers.VALID_CUSTOMER_EMAIL
                val amount = BigDecimal("100.00")
                val currency = "USD"
                val billingAddress = TestDataFixtures.Addresses.validBillingAddress()
                val deviceFingerprint = TestDataFixtures.DeviceFingerprints.validDeviceFingerprint()
                
                // Mock payment session
                val paymentSession = TestDataFixtures.PaymentSessions.validPaymentSession()
                every { cybersourceService.getPaymentSession(paymentId) } returns paymentSession
                
                // Mock JWT verification
                val mockFlexJwt = mockk<Any>()
                every { CybersourceUtils.verifyJwtAndGetDecodedBody(paymentData) } returns mockFlexJwt
                
                // Mock PaymentsApi
                val mockPaymentsApi = MockUtils.CybersourceApiMocks.createMockPaymentsApi()
                every { PaymentsApi(mockApiClient) } returns mockPaymentsApi

                // Act
                val result = cybersourceService.authorizeWithCreditCard(
                    paymentId = paymentId,
                    paymentData = paymentData,
                    paymentInstrumentId = null,
                    customerTokenId = customerTokenId,
                    customerId = customerId,
                    customerEmail = customerEmail,
                    customerPhone = null,
                    amount = amount,
                    currency = currency,
                    storeInVault = true,
                    billingAddress = billingAddress,
                    deviceFingerprint = deviceFingerprint
                )

                // Assert
                result shouldNotBe null
                result.paymentId shouldBe paymentId
                result.amount shouldBe TestDataFixtures.Payments.VALID_AMOUNT
                result.currency shouldBe currency
                verify { mockPaymentRepository.addPayment(paymentId, customerId, customerTokenId, amount, currency, PaymentMethod.CREDIT_CARD) }
                verify { mockPaymentsApi.createPayment(any()) }
            }

            it("should authorize vault payment successfully") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val paymentInstrumentId = TestDataFixtures.PaymentInstruments.VALID_PAYMENT_INSTRUMENT_ID
                val customerTokenId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val customerId = TestDataFixtures.Customers.VALID_CUSTOMER_ID
                val customerEmail = TestDataFixtures.Customers.VALID_CUSTOMER_EMAIL
                val amount = BigDecimal("100.00")
                val currency = "USD"
                
                // Mock payment session
                val paymentSession = TestDataFixtures.PaymentSessions.validPaymentSession()
                every { cybersourceService.getPaymentSession(paymentId) } returns paymentSession
                
                // Mock getPaymentInstruments
                every { cybersourceService.getPaymentInstruments(customerTokenId) } returns listOf(
                    TestDataFixtures.PaymentInstruments.validPaymentInstrument()
                )
                
                // Mock PaymentsApi
                val mockPaymentsApi = MockUtils.CybersourceApiMocks.createMockPaymentsApi()
                every { PaymentsApi(mockApiClient) } returns mockPaymentsApi

                // Act
                val result = cybersourceService.authorizeWithCreditCard(
                    paymentId = paymentId,
                    paymentData = null,
                    paymentInstrumentId = paymentInstrumentId,
                    customerTokenId = customerTokenId,
                    customerId = customerId,
                    customerEmail = customerEmail,
                    customerPhone = null,
                    amount = amount,
                    currency = currency,
                    storeInVault = false,
                    billingAddress = null,
                    deviceFingerprint = null
                )

                // Assert
                result shouldNotBe null
                result.paymentId shouldBe paymentId
                verify { mockPaymentsApi.createPayment(any()) }
            }

            it("should throw BadRequestException when payment is declined") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val paymentData = "mock-jwt-token"
                val customerTokenId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val customerId = TestDataFixtures.Customers.VALID_CUSTOMER_ID
                val customerEmail = TestDataFixtures.Customers.VALID_CUSTOMER_EMAIL
                val amount = BigDecimal("100.00")
                val currency = "USD"
                
                // Mock payment session
                val paymentSession = TestDataFixtures.PaymentSessions.validPaymentSession()
                every { cybersourceService.getPaymentSession(paymentId) } returns paymentSession
                
                // Mock JWT verification
                val mockFlexJwt = mockk<Any>()
                every { CybersourceUtils.verifyJwtAndGetDecodedBody(paymentData) } returns mockFlexJwt
                
                // Mock PaymentsApi to return declined status
                val mockPaymentsApi = mockk<PaymentsApi>()
                val mockResponse = mockk<PtsV2PaymentsPost201Response>()
                every { PaymentsApi(mockApiClient) } returns mockPaymentsApi
                every { mockPaymentsApi.createPayment(any()) } returns mockResponse
                every { mockResponse.id } returns "declined-auth-id"
                every { mockResponse.status } returns "DECLINED"
                every { mockResponse.submitTimeUtc } returns "2024-01-15T10:30:00Z"

                // Act & Assert
                val exception = shouldThrow<AppException.BadRequestException> {
                    cybersourceService.authorizeWithCreditCard(
                        paymentId = paymentId,
                        paymentData = paymentData,
                        paymentInstrumentId = null,
                        customerTokenId = customerTokenId,
                        customerId = customerId,
                        customerEmail = customerEmail,
                        customerPhone = null,
                        amount = amount,
                        currency = currency,
                        storeInVault = false,
                        billingAddress = null,
                        deviceFingerprint = null
                    )
                }
                exception.message shouldBe "Payment declined."
            }

            it("should handle API exceptions properly") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val paymentData = "mock-jwt-token"
                val customerTokenId = TestDataFixtures.Customers.VALID_PROCESSOR_ID
                val customerId = TestDataFixtures.Customers.VALID_CUSTOMER_ID
                val customerEmail = TestDataFixtures.Customers.VALID_CUSTOMER_EMAIL
                val amount = BigDecimal("100.00")
                val currency = "USD"
                
                // Mock payment session
                val paymentSession = TestDataFixtures.PaymentSessions.validPaymentSession()
                every { cybersourceService.getPaymentSession(paymentId) } returns paymentSession
                
                // Mock JWT verification
                val mockFlexJwt = mockk<Any>()
                every { CybersourceUtils.verifyJwtAndGetDecodedBody(paymentData) } returns mockFlexJwt
                
                // Mock PaymentsApi to throw exception
                val mockPaymentsApi = mockk<PaymentsApi>()
                every { PaymentsApi(mockApiClient) } returns mockPaymentsApi
                val errorResponse = """{"id":"error-123","reason":"INVALID_CARD","message":"Invalid card","status":"INVALID_REQUEST","details":[]}"""
                every { mockPaymentsApi.createPayment(any()) } throws ApiException(400, errorResponse)

                // Act & Assert
                val exception = shouldThrow<AppException.BadRequestException> {
                    cybersourceService.authorizeWithCreditCard(
                        paymentId = paymentId,
                        paymentData = paymentData,
                        paymentInstrumentId = null,
                        customerTokenId = customerTokenId,
                        customerId = customerId,
                        customerEmail = customerEmail,
                        customerPhone = null,
                        amount = amount,
                        currency = currency,
                        storeInVault = false,
                        billingAddress = null,
                        deviceFingerprint = null
                    )
                }
                exception.message shouldBe "Card or billing data missing"
            }
        }
    }

    describe("Payment Capture") {
        
        describe("captureAuthorization") {
            it("should capture authorized payment successfully") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val authorizedPayment = TestDataFixtures.Payments.validPayment().copy(
                    paymentStatus = PaymentStatus.AUTHORIZED
                )
                val paymentEvent = TestDataFixtures.Payments.validPaymentEvent().copy(
                    paymentEventType = PaymentEventType.AUTHORIZATION,
                    paymentEventStatus = PaymentEventStatus.SUCCESS
                )
                
                every { mockPaymentRepository.findPaymentById(paymentId) } returns authorizedPayment
                every { mockPaymentRepository.findPaymentEventsByPaymentId(paymentId) } returns listOf(paymentEvent)
                
                val mockCaptureApi = MockUtils.CybersourceApiMocks.createMockCaptureApi()
                every { CaptureApi(mockApiClient) } returns mockCaptureApi

                // Act
                val result = cybersourceService.captureAuthorization(paymentId)

                // Assert
                result shouldNotBe null
                result.paymentId shouldBe paymentId
                result.amount shouldBe TestDataFixtures.Payments.VALID_AMOUNT
                verify { mockCaptureApi.capturePayment(any(), paymentEvent.referenceId!!) }
                verify { mockPaymentRepository.updatePayment(paymentId, PaymentStatus.CAPTURED, any()) }
            }

            it("should throw ConflictException when payment is not authorized") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val capturedPayment = TestDataFixtures.Payments.validPayment().copy(
                    paymentStatus = PaymentStatus.CAPTURED
                )
                
                every { mockPaymentRepository.findPaymentById(paymentId) } returns capturedPayment

                // Act & Assert
                val exception = shouldThrow<AppException.ConflictException> {
                    cybersourceService.captureAuthorization(paymentId)
                }
                exception.message shouldBe "Transaction already processed or not in correct state"
            }

            it("should throw BadRequestException when no authorization events found") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val authorizedPayment = TestDataFixtures.Payments.validPayment().copy(
                    paymentStatus = PaymentStatus.AUTHORIZED
                )
                
                every { mockPaymentRepository.findPaymentById(paymentId) } returns authorizedPayment
                every { mockPaymentRepository.findPaymentEventsByPaymentId(paymentId) } returns emptyList()

                // Act & Assert
                val exception = shouldThrow<AppException.BadRequestException> {
                    cybersourceService.captureAuthorization(paymentId)
                }
                exception.message shouldBe "No authorization events found for payment $paymentId"
            }
        }
    }

    describe("Payment Reversal") {
        
        describe("reverseAuthorization") {
            it("should reverse authorized payment successfully") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val reason = "Customer requested cancellation"
                val authorizedPayment = TestDataFixtures.Payments.validPayment().copy(
                    paymentStatus = PaymentStatus.AUTHORIZED
                )
                val paymentEvent = TestDataFixtures.Payments.validPaymentEvent().copy(
                    paymentEventType = PaymentEventType.AUTHORIZATION,
                    paymentEventStatus = PaymentEventStatus.SUCCESS
                )
                
                every { mockPaymentRepository.findPaymentById(paymentId) } returns authorizedPayment
                every { mockPaymentRepository.findPaymentEventsByPaymentId(paymentId) } returns listOf(paymentEvent)
                
                val mockReversalApi = MockUtils.CybersourceApiMocks.createMockReversalApi()
                every { ReversalApi(mockApiClient) } returns mockReversalApi

                // Act
                val result = cybersourceService.reverseAuthorization(paymentId, reason)

                // Assert
                result shouldNotBe null
                result.paymentId shouldBe paymentId
                result.amount shouldBe TestDataFixtures.Payments.VALID_AMOUNT
                verify { mockReversalApi.authReversal(paymentEvent.referenceId!!, any()) }
                verify { mockPaymentRepository.updatePayment(paymentId, PaymentStatus.REVERSED, any()) }
            }

            it("should throw ConflictException when payment is not authorized") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val reason = "Customer requested cancellation"
                val capturedPayment = TestDataFixtures.Payments.validPayment().copy(
                    paymentStatus = PaymentStatus.CAPTURED
                )
                
                every { mockPaymentRepository.findPaymentById(paymentId) } returns capturedPayment

                // Act & Assert
                val exception = shouldThrow<AppException.ConflictException> {
                    cybersourceService.reverseAuthorization(paymentId, reason)
                }
                exception.message shouldBe "Transaction already processed or not in correct state"
            }
        }
    }

    describe("Payment Void") {
        
        describe("voidCapture") {
            it("should void captured payment successfully") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val capturedPayment = TestDataFixtures.Payments.validPayment().copy(
                    paymentStatus = PaymentStatus.CAPTURED
                )
                val captureEvent = TestDataFixtures.Payments.validPaymentEvent().copy(
                    paymentEventType = PaymentEventType.CAPTURE,
                    paymentEventStatus = PaymentEventStatus.SUCCESS
                )
                
                every { mockPaymentRepository.findPaymentById(paymentId) } returns capturedPayment
                every { mockPaymentRepository.findPaymentEventsByPaymentId(paymentId) } returns listOf(captureEvent)
                
                val mockVoidApi = MockUtils.CybersourceApiMocks.createMockVoidApi()
                every { VoidApi(mockApiClient) } returns mockVoidApi

                // Act
                val result = cybersourceService.voidCapture(paymentId)

                // Assert
                result shouldNotBe null
                result.paymentId shouldBe paymentId
                result.amount shouldBe TestDataFixtures.Payments.VALID_AMOUNT
                verify { mockVoidApi.voidPayment(captureEvent.referenceId!!, any()) }
                verify { mockPaymentRepository.updatePayment(paymentId, PaymentStatus.VOIDED, any()) }
            }

            it("should throw ConflictException when payment is not captured") {
                // Arrange
                val paymentId = TestDataFixtures.Payments.VALID_PAYMENT_ID
                val authorizedPayment = TestDataFixtures.Payments.validPayment().copy(
                    paymentStatus = PaymentStatus.AUTHORIZED
                )
                
                every { mockPaymentRepository.findPaymentById(paymentId) } returns authorizedPayment

                // Act & Assert
                val exception = shouldThrow<AppException.ConflictException> {
                    cybersourceService.voidCapture(paymentId)
                }
                exception.message shouldBe "Transaction already processed or not in correct state"
            }
        }
    }
})
