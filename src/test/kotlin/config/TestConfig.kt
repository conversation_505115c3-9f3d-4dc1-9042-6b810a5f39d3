package config

import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import database.DatabaseFactory
import io.ktor.server.application.*
import io.ktor.server.testing.*
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction
import plugins.*
import repositories.CustomerRepository
import repositories.PaymentRepository
import services.CybersourceService
import usecases.CustomerUseCase
import utils.MockUtils
import javax.sql.DataSource

/**
 * Test configuration for setting up test environment
 */
object TestConfig {

    /**
     * Creates a test application with mocked dependencies
     */
    fun createTestApplication(): ApplicationTestBuilder.() -> Unit = {
        application {
            configureTestApplication()
        }
    }

    /**
     * Creates a test application with real database (for integration tests)
     */
    fun createIntegrationTestApplication(): ApplicationTestBuilder.() -> Unit = {
        application {
            configureIntegrationTestApplication()
        }
    }

    /**
     * Configures the test application with mocked dependencies
     */
    private fun Application.configureTestApplication() {
        // Configure plugins
        configureSerialization()
        configureApi()
        configureStatusPages()

        // Create mock dependencies
        val mockCybersourceService = MockUtils.createMockCybersourceService()
        val mockCustomerRepository = MockUtils.createMockCustomerRepository()
        val mockPaymentRepository = MockUtils.createMockPaymentRepository()
        val mockCustomerUseCase = MockUtils.createMockCustomerUseCase()

        // Configure routing with mocked dependencies
        configureRouting(
            cybersourceService = mockCybersourceService,
            customerRepository = mockCustomerRepository,
            customerUseCase = mockCustomerUseCase,
            paymentRepository = mockPaymentRepository
        )
    }

    /**
     * Configures the integration test application with real database
     */
    private fun Application.configureIntegrationTestApplication() {
        // Configure plugins
        configureSerialization()
        configureApi()
        configureStatusPages()

        // Set up test database
        val testDatabase = setupTestDatabase()
        
        // Create real repositories with test database
        val customerRepository = CustomerRepository()
        val paymentRepository = PaymentRepository()
        val customerUseCase = CustomerUseCase(customerRepository, MockUtils.createMockCybersourceService())

        // Use mock Cybersource service for integration tests to avoid external API calls
        val mockCybersourceService = MockUtils.createMockCybersourceService()

        // Configure routing with real repositories but mocked external service
        configureRouting(
            cybersourceService = mockCybersourceService,
            customerRepository = customerRepository,
            customerUseCase = customerUseCase,
            paymentRepository = paymentRepository
        )
    }

    /**
     * Sets up an in-memory H2 database for testing
     */
    private fun setupTestDatabase(): Database {
        val dataSource = createTestDataSource()
        val database = Database.connect(dataSource)
        
        // Create tables
        transaction(database) {
            SchemaUtils.create(
                entities.Customers,
                entities.Payments,
                entities.PaymentEvents
            )
        }
        
        return database
    }

    /**
     * Creates a test data source using H2 in-memory database
     */
    private fun createTestDataSource(): DataSource {
        val config = HikariConfig().apply {
            jdbcUrl = "jdbc:h2:mem:test_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE"
            driverClassName = "org.h2.Driver"
            username = "sa"
            password = ""
            maximumPoolSize = 10
            minimumIdle = 1
            connectionTimeout = 30000
            idleTimeout = 600000
            maxLifetime = 1800000
        }
        return HikariDataSource(config)
    }

    /**
     * Test database configuration
     */
    object TestDatabase {
        
        /**
         * Cleans all test data from the database
         */
        fun cleanDatabase() {
            transaction {
                // Clean up in reverse order of dependencies
                entities.PaymentEvents.deleteAll()
                entities.Payments.deleteAll()
                entities.Customers.deleteAll()
            }
        }

        /**
         * Sets up basic test data in the database
         */
        fun setupBasicTestData() {
            transaction {
                // Insert test customer
                entities.Customers.insert {
                    it[id] = "test-customer-123"
                    it[processorId] = "cybersource-customer-token-123"
                    it[status] = models.AccountStatus.OPEN.name
                }

                // Insert test payment
                entities.Payments.insert {
                    it[paymentId] = "test-payment-123"
                    it[customerId] = "test-customer-123"
                    it[customerTokenId] = "cybersource-customer-token-123"
                    it[amount] = java.math.BigDecimal("100.00")
                    it[currency] = "USD"
                    it[paymentStatus] = models.PaymentStatus.PENDING.name
                    it[paymentMethod] = models.PaymentMethod.CREDIT_CARD.name
                }
            }
        }
    }

    /**
     * Test environment configuration
     */
    object Environment {
        
        /**
         * Sets up test environment variables
         */
        fun setupTestEnvironment() {
            System.setProperty("ktor.environment", "test")
            System.setProperty("cybersource.environment", "sandbox")
            System.setProperty("database.url", "jdbc:h2:mem:test_db")
        }

        /**
         * Cleans up test environment
         */
        fun cleanupTestEnvironment() {
            System.clearProperty("ktor.environment")
            System.clearProperty("cybersource.environment")
            System.clearProperty("database.url")
        }
    }

    /**
     * Mock configuration for external services
     */
    object MockConfiguration {
        
        /**
         * Sets up all necessary mocks for testing
         */
        fun setupMocks() {
            MockUtils.mockCybersourceUtils()
        }

        /**
         * Cleans up all mocks after testing
         */
        fun cleanupMocks() {
            // Clear all mockk mocks
            io.mockk.clearAllMocks()
        }
    }

    /**
     * Test data configuration
     */
    object TestData {
        
        /**
         * Creates test payment session data
         */
        fun createTestPaymentSession(): models.Response.PaymentSession {
            return fixtures.TestDataFixtures.PaymentSessions.validPaymentSession()
        }

        /**
         * Creates test customer data
         */
        fun createTestCustomer(): models.Customer {
            return fixtures.TestDataFixtures.Customers.validCustomer()
        }

        /**
         * Creates test payment data
         */
        fun createTestPayment(): models.Payment {
            return fixtures.TestDataFixtures.Payments.validPayment()
        }

        /**
         * Creates test authorization request
         */
        fun createTestAuthorizationRequest(): models.Request.Authorize {
            return fixtures.TestDataFixtures.AuthorizeRequests.validCreditCardAuthorizeRequest()
        }
    }

    /**
     * Test timeouts and delays
     */
    object Timeouts {
        const val DEFAULT_TIMEOUT_MS = 5000L
        const val LONG_TIMEOUT_MS = 10000L
        const val SHORT_TIMEOUT_MS = 1000L
        
        const val DEFAULT_DELAY_MS = 100L
        const val SHORT_DELAY_MS = 50L
    }

    /**
     * Test URLs and endpoints
     */
    object Endpoints {
        const val BASE_URL = "/api/v1"
        
        // Payment endpoints
        const val PAYMENT_SESSIONS = "$BASE_URL/payment-sessions"
        const val PAYMENTS = "$BASE_URL/payments"
        
        // Customer endpoints
        const val CUSTOMERS = "$BASE_URL/customers"
        
        // Health endpoints
        const val HEALTH = "/healthz"
        const val READY = "/ready"
        
        fun paymentAuthorize(paymentId: String) = "$PAYMENTS/$paymentId/authorize"
        fun paymentCapture(paymentId: String) = "$PAYMENTS/$paymentId/capture"
        fun paymentReverse(paymentId: String) = "$PAYMENTS/$paymentId/reverse"
        fun paymentVoid(paymentId: String) = "$PAYMENTS/$paymentId/void"
        
        fun customer(customerId: String) = "$CUSTOMERS/$customerId"
        fun paymentInstrument(customerId: String, instrumentId: String) = 
            "$CUSTOMERS/$customerId/payment-instruments/$instrumentId"
    }

    /**
     * Test assertions configuration
     */
    object Assertions {
        const val ASSERTION_TIMEOUT_MS = 1000L
        const val MAX_RETRY_ATTEMPTS = 3
        const val RETRY_DELAY_MS = 100L
    }
}
