package integration

import config.TestConfig
import fixtures.TestDataFixtures
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.ktor.http.*
import io.ktor.server.testing.*
import models.*
import utils.TestUtils

/**
 * Integration tests for Payment Operations endpoints
 */
class PaymentOperationsIntegrationTest : DescribeSpec({

    describe("Payment Operations API") {
        
        describe("Payment Authorization") {
            
            it("should authorize credit card payment successfully") {
                testApplication(TestConfig.createTestApplication()) {
                    // First create a payment session
                    val sessionRequest = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    // Arrange authorization request
                    val authRequest = TestDataFixtures.AuthorizeRequests.validCreditCardAuthorizeRequest()

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val authResponse = TestUtils.Assertions.assertResponseBody<Response.Authorized>(response)
                    TestUtils.Assertions.assertValidAuthorizationResponse(authResponse)
                    
                    authResponse.paymentId shouldBe paymentSession.paymentId
                    authResponse.amount shouldBe TestDataFixtures.Payments.VALID_AMOUNT
                    authResponse.currency shouldBe TestDataFixtures.Payments.VALID_CURRENCY
                }
            }

            it("should authorize vault payment successfully") {
                testApplication(TestConfig.createTestApplication()) {
                    // Create payment session for vault payment
                    val sessionRequest = Request.PaymentSession(
                        paymentMethod = PaymentMethod.VAULT.name,
                        deviceType = DeviceType.WEB.name
                    )
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    // Arrange vault authorization request
                    val authRequest = TestDataFixtures.AuthorizeRequests.validVaultAuthorizeRequest()

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val authResponse = TestUtils.Assertions.assertResponseBody<Response.Authorized>(response)
                    TestUtils.Assertions.assertValidAuthorizationResponse(authResponse)
                }
            }

            it("should authorize default vault payment successfully") {
                testApplication(TestConfig.createTestApplication()) {
                    // Create payment session for default vault payment
                    val sessionRequest = Request.PaymentSession(
                        paymentMethod = PaymentMethod.DEFAULT_VAULT.name,
                        deviceType = DeviceType.WEB.name
                    )
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    // Arrange default vault authorization request
                    val authRequest = TestDataFixtures.AuthorizeRequests.validDefaultVaultAuthorizeRequest()

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val authResponse = TestUtils.Assertions.assertResponseBody<Response.Authorized>(response)
                    TestUtils.Assertions.assertValidAuthorizationResponse(authResponse)
                }
            }

            it("should return 400 for missing credit card details") {
                testApplication(TestConfig.createTestApplication()) {
                    // Create payment session
                    val sessionRequest = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    // Arrange authorization request without credit card details
                    val authRequest = Request.Authorize(
                        amount = TestDataFixtures.Payments.VALID_AMOUNT,
                        currency = TestDataFixtures.Payments.VALID_CURRENCY,
                        customerDetail = TestDataFixtures.Customers.validCustomerDetail(),
                        creditCardDetail = null, // Missing for credit card payment
                        vaultDetails = null,
                        billingAddress = null,
                        deviceFingerprint = null
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                    TestUtils.Assertions.assertErrorResponse(response, "INVALID_REQUEST")
                }
            }

            it("should return 400 for missing vault details") {
                testApplication(TestConfig.createTestApplication()) {
                    // Create vault payment session
                    val sessionRequest = Request.PaymentSession(
                        paymentMethod = PaymentMethod.VAULT.name,
                        deviceType = DeviceType.WEB.name
                    )
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    // Arrange authorization request without vault details
                    val authRequest = Request.Authorize(
                        amount = TestDataFixtures.Payments.VALID_AMOUNT,
                        currency = TestDataFixtures.Payments.VALID_CURRENCY,
                        customerDetail = TestDataFixtures.Customers.validCustomerDetail(),
                        creditCardDetail = null,
                        vaultDetails = null, // Missing for vault payment
                        billingAddress = null,
                        deviceFingerprint = null
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                    TestUtils.Assertions.assertErrorResponse(response, "INVALID_REQUEST")
                }
            }

            it("should return 400 for invalid amount") {
                testApplication(TestConfig.createTestApplication()) {
                    // Create payment session
                    val sessionRequest = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    // Arrange authorization request with invalid amount
                    val authRequest = Request.Authorize(
                        amount = "invalid-amount",
                        currency = TestDataFixtures.Payments.VALID_CURRENCY,
                        customerDetail = TestDataFixtures.Customers.validCustomerDetail(),
                        creditCardDetail = TestDataFixtures.CreditCards.validCreditCardDetail(),
                        vaultDetails = null,
                        billingAddress = TestDataFixtures.Addresses.validBillingAddress(),
                        deviceFingerprint = null
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                }
            }

            it("should return 400 for invalid currency") {
                testApplication(TestConfig.createTestApplication()) {
                    // Create payment session
                    val sessionRequest = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    // Arrange authorization request with invalid currency
                    val authRequest = Request.Authorize(
                        amount = TestDataFixtures.Payments.VALID_AMOUNT,
                        currency = "INVALID",
                        customerDetail = TestDataFixtures.Customers.validCustomerDetail(),
                        creditCardDetail = TestDataFixtures.CreditCards.validCreditCardDetail(),
                        vaultDetails = null,
                        billingAddress = TestDataFixtures.Addresses.validBillingAddress(),
                        deviceFingerprint = null
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                }
            }

            it("should return 404 for non-existent payment session") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange authorization request with non-existent payment ID
                    val authRequest = TestDataFixtures.AuthorizeRequests.validCreditCardAuthorizeRequest()

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize("non-existent-payment-id"),
                        body = authRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.NotFound)
                }
            }
        }

        describe("Payment Capture") {
            
            it("should capture authorized payment successfully") {
                testApplication(TestConfig.createTestApplication()) {
                    // Create and authorize a payment first
                    val sessionRequest = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    val authRequest = TestDataFixtures.AuthorizeRequests.validCreditCardAuthorizeRequest()
                    val authResponse = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )
                    TestUtils.Assertions.assertResponseStatus(authResponse, HttpStatusCode.OK)

                    // Arrange capture request
                    val captureRequest = Request.CreditCardCapture(paymentId = paymentSession.paymentId)

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentCapture(paymentSession.paymentId),
                        body = captureRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val captureResponse = TestUtils.Assertions.assertResponseBody<Response.Captured>(response)
                    TestUtils.Assertions.assertValidCaptureResponse(captureResponse)
                    
                    captureResponse.paymentId shouldBe paymentSession.paymentId
                    captureResponse.amount shouldBe TestDataFixtures.Payments.VALID_AMOUNT
                }
            }

            it("should return 400 for empty payment ID") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange capture request with empty payment ID
                    val captureRequest = Request.CreditCardCapture(paymentId = "")

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentCapture("some-payment-id"),
                        body = captureRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                }
            }
        }

        describe("Payment Reversal") {
            
            it("should reverse authorized payment successfully") {
                testApplication(TestConfig.createTestApplication()) {
                    // Create and authorize a payment first
                    val sessionRequest = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    val authRequest = TestDataFixtures.AuthorizeRequests.validCreditCardAuthorizeRequest()
                    val authResponse = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )
                    TestUtils.Assertions.assertResponseStatus(authResponse, HttpStatusCode.OK)

                    // Arrange reversal request
                    val reversalRequest = Request.CreditCardReverse(
                        paymentId = paymentSession.paymentId,
                        reason = "Customer requested cancellation"
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentReverse(paymentSession.paymentId),
                        body = reversalRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val reversalResponse = TestUtils.Assertions.assertResponseBody<Response.Reversed>(response)
                    
                    reversalResponse.paymentId shouldBe paymentSession.paymentId
                    reversalResponse.amount shouldBe TestDataFixtures.Payments.VALID_AMOUNT
                }
            }

            it("should return 400 for empty reason") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange reversal request with empty reason
                    val reversalRequest = Request.CreditCardReverse(
                        paymentId = "some-payment-id",
                        reason = ""
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentReverse("some-payment-id"),
                        body = reversalRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                }
            }
        }

        describe("Payment Void") {
            
            it("should void captured payment successfully") {
                testApplication(TestConfig.createTestApplication()) {
                    // Create, authorize, and capture a payment first
                    val sessionRequest = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()
                    val sessionResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = sessionRequest
                    )
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(sessionResponse)

                    // Authorize
                    val authRequest = TestDataFixtures.AuthorizeRequests.validCreditCardAuthorizeRequest()
                    val authResponse = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentAuthorize(paymentSession.paymentId),
                        body = authRequest
                    )
                    TestUtils.Assertions.assertResponseStatus(authResponse, HttpStatusCode.OK)

                    // Capture
                    val captureRequest = Request.CreditCardCapture(paymentId = paymentSession.paymentId)
                    val captureResponse = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentCapture(paymentSession.paymentId),
                        body = captureRequest
                    )
                    TestUtils.Assertions.assertResponseStatus(captureResponse, HttpStatusCode.OK)

                    // Arrange void request
                    val voidRequest = Request.CreditCardVoid(paymentId = paymentSession.paymentId)

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentVoid(paymentSession.paymentId),
                        body = voidRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val voidResponse = TestUtils.Assertions.assertResponseBody<Response.Voided>(response)
                    
                    voidResponse.paymentId shouldBe paymentSession.paymentId
                    voidResponse.amount shouldBe TestDataFixtures.Payments.VALID_AMOUNT
                }
            }

            it("should return 400 for empty payment ID") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange void request with empty payment ID
                    val voidRequest = Request.CreditCardVoid(paymentId = "")

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestConfig.Endpoints.paymentVoid("some-payment-id"),
                        body = voidRequest
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                }
            }
        }
    }
})
