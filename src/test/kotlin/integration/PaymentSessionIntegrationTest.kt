package integration

import config.TestConfig
import fixtures.TestDataFixtures
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.testing.*
import models.*
import utils.TestUtils

/**
 * Integration tests for Payment Session endpoints
 */
class PaymentSessionIntegrationTest : DescribeSpec({

    describe("Payment Session API") {
        
        describe("POST /api/v1/payment-sessions") {
            
            it("should create payment session successfully with credit card method") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange
                    val request = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = request
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(response)
                    TestUtils.Assertions.assertValidPaymentSession(paymentSession)
                    
                    paymentSession.paymentMethod shouldBe PaymentMethod.CREDIT_CARD.name
                    paymentSession.deviceType shouldBe DeviceType.WEB.name
                }
            }

            it("should create payment session successfully with vault method") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange
                    val request = Request.PaymentSession(
                        paymentMethod = PaymentMethod.VAULT.name,
                        deviceType = DeviceType.WEB.name
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = request
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(response)
                    TestUtils.Assertions.assertValidPaymentSession(paymentSession)
                    
                    paymentSession.paymentMethod shouldBe PaymentMethod.VAULT.name
                }
            }

            it("should create payment session successfully with default vault method") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange
                    val request = Request.PaymentSession(
                        paymentMethod = PaymentMethod.DEFAULT_VAULT.name,
                        deviceType = DeviceType.ANDROID.name
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = request
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(response)
                    TestUtils.Assertions.assertValidPaymentSession(paymentSession)
                    
                    paymentSession.paymentMethod shouldBe PaymentMethod.DEFAULT_VAULT.name
                    paymentSession.deviceType shouldBe DeviceType.ANDROID.name
                }
            }

            it("should return 400 for invalid payment method") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange
                    val request = Request.PaymentSession(
                        paymentMethod = "INVALID_METHOD",
                        deviceType = DeviceType.WEB.name
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = request
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                }
            }

            it("should return 400 for invalid device type") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange
                    val request = Request.PaymentSession(
                        paymentMethod = PaymentMethod.CREDIT_CARD.name,
                        deviceType = "INVALID_DEVICE"
                    )

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = request
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                }
            }

            it("should return 400 for empty request body") {
                testApplication(TestConfig.createTestApplication()) {
                    // Act
                    val response = client.post(TestUtils.Constants.PAYMENT_SESSIONS_PATH) {
                        contentType(ContentType.Application.Json)
                        setBody("{}")
                    }

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                }
            }

            it("should return 400 for malformed JSON") {
                testApplication(TestConfig.createTestApplication()) {
                    // Act
                    val response = client.post(TestUtils.Constants.PAYMENT_SESSIONS_PATH) {
                        contentType(ContentType.Application.Json)
                        setBody("invalid json")
                    }

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.BadRequest)
                }
            }

            it("should handle all supported payment methods") {
                testApplication(TestConfig.createTestApplication()) {
                    val supportedMethods = listOf(
                        PaymentMethod.CREDIT_CARD,
                        PaymentMethod.VAULT,
                        PaymentMethod.DEFAULT_VAULT
                        // Note: PAYPAL, GOOGLE_PAY, APPLE_PAY are not yet implemented
                    )

                    for (method in supportedMethods) {
                        // Arrange
                        val request = Request.PaymentSession(
                            paymentMethod = method.name,
                            deviceType = DeviceType.WEB.name
                        )

                        // Act
                        val response = TestUtils.Http.postRequest(
                            url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                            body = request
                        )

                        // Assert
                        TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                        val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(response)
                        paymentSession.paymentMethod shouldBe method.name
                    }
                }
            }

            it("should handle all supported device types") {
                testApplication(TestConfig.createTestApplication()) {
                    val supportedDeviceTypes = DeviceType.entries

                    for (deviceType in supportedDeviceTypes) {
                        // Arrange
                        val request = Request.PaymentSession(
                            paymentMethod = PaymentMethod.CREDIT_CARD.name,
                            deviceType = deviceType.name
                        )

                        // Act
                        val response = TestUtils.Http.postRequest(
                            url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                            body = request
                        )

                        // Assert
                        TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                        val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(response)
                        paymentSession.deviceType shouldBe deviceType.name
                    }
                }
            }

            it("should generate unique payment IDs for concurrent requests") {
                testApplication(TestConfig.createTestApplication()) {
                    val request = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()
                    val paymentIds = mutableSetOf<String>()

                    // Create multiple payment sessions concurrently
                    repeat(5) {
                        val response = TestUtils.Http.postRequest(
                            url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                            body = request
                        )

                        TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                        val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(response)
                        paymentIds.add(paymentSession.paymentId)
                    }

                    // Assert all payment IDs are unique
                    paymentIds.size shouldBe 5
                }
            }

            it("should include all required fields in response") {
                testApplication(TestConfig.createTestApplication()) {
                    // Arrange
                    val request = TestDataFixtures.PaymentSessions.validPaymentSessionRequest()

                    // Act
                    val response = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = request
                    )

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(response)
                    
                    // Verify all required fields are present and not empty
                    TestUtils.Validation.isValidPaymentId(paymentSession.paymentId) shouldBe true
                    paymentSession.paymentSignature.isNotBlank() shouldBe true
                    paymentSession.deviceType.isNotBlank() shouldBe true
                    paymentSession.paymentMethod.isNotBlank() shouldBe true
                    paymentSession.organizationId.isNotBlank() shouldBe true
                    paymentSession.merchantId.isNotBlank() shouldBe true
                }
            }

            it("should handle request with extra fields gracefully") {
                testApplication(TestConfig.createTestApplication()) {
                    // Act
                    val response = client.post(TestUtils.Constants.PAYMENT_SESSIONS_PATH) {
                        contentType(ContentType.Application.Json)
                        setBody("""
                            {
                                "paymentMethod": "CREDIT_CARD",
                                "deviceType": "WEB",
                                "extraField": "should be ignored",
                                "anotherField": 123
                            }
                        """.trimIndent())
                    }

                    // Assert
                    TestUtils.Assertions.assertResponseStatus(response, HttpStatusCode.OK)
                    val paymentSession = TestUtils.Assertions.assertResponseBody<Response.PaymentSession>(response)
                    TestUtils.Assertions.assertValidPaymentSession(paymentSession)
                }
            }

            it("should handle case-sensitive payment method and device type") {
                testApplication(TestConfig.createTestApplication()) {
                    // Test lowercase - should fail
                    val lowercaseRequest = client.post(TestUtils.Constants.PAYMENT_SESSIONS_PATH) {
                        contentType(ContentType.Application.Json)
                        setBody("""
                            {
                                "paymentMethod": "credit_card",
                                "deviceType": "web"
                            }
                        """.trimIndent())
                    }

                    TestUtils.Assertions.assertResponseStatus(lowercaseRequest, HttpStatusCode.BadRequest)

                    // Test correct case - should succeed
                    val correctRequest = Request.PaymentSession(
                        paymentMethod = PaymentMethod.CREDIT_CARD.name,
                        deviceType = DeviceType.WEB.name
                    )

                    val correctResponse = TestUtils.Http.postRequest(
                        url = TestUtils.Constants.PAYMENT_SESSIONS_PATH,
                        body = correctRequest
                    )

                    TestUtils.Assertions.assertResponseStatus(correctResponse, HttpStatusCode.OK)
                }
            }
        }
    }
})
