app {
    name = "purchase-api"
    dns = "purchase-api"
    database {
      driverClassName="org.postgresql.Driver"
      maxPoolSize=1

      hostname=${?POSTGRES_HOSTNAME}
      port=${?POSTGRES_PORT}
      user=${?POSTGRES_USER}
      password=${?POSTGRES_PASSWORD}

      schema="public"
      database_default="purchase_api"
    }
    cybersource {
      merchant_id = ${CYBERSOURCE_MERCHANT_ID}
      organization_id = ${CYBERSOURCE_ORGANIZATION_ID}
      run_environment = ${CYBERSOURCE_RUN_ENVIRONMENT}
      flex_environment = ${CYBERSOURCE_FLEX_ENVIRONMENT}
      profiler_url = ${CYBERSOURCE_PROFILER_URL}
      profile_id = ${CYBERSOURCE_PROFILE_ID}
      key_id = ${CYBERSOURCE_KEY_ID}
      key_secret = ${CYBERSOURCE_KEY_SECRET}
      micro_form_origin = ${?CYBERSOURCE_MICRO_FORM_ORIGIN}
    }
    apple {
      issuer_id = ${APPLE_ISSUER_ID}
      key_id = ${APPLE_KEY_ID}
      bundle_id = ${APPLE_BUNDLE_ID}
      app_apple_id = ${APPLE_APP_APPLE_ID}
      private_key = ${APPLE_PRIVATE_KEY}
      cas = ${APPLE_CAS}
      apple_pay_merchant_identifier = ${APPLE_PAY_MERCHANT_IDENTIFIER}
      apple_pay_key_path = ${APPLE_PAY_KEY_PATH}
      apple_pay_certificate_path = ${APPLE_PAY_CERTIFICATE_PATH}
    }
    google-play {
      resource_url = ${GOOGLE_PLAY_RESOURCE_URL}
      service_account_email = ${GOOGLE_PLAY_SERVICE_ACCOUNT_EMAIL}
      application_name = ${GOOGLE_PLAY_APPLICATION_NAME}
    }
    paypal {
      client_id = ${PAYPAL_CHECKOUT_CLIENT_ID}
      client_secret = ${PAYPAL_CLIENT_SECRET}
    }
}

ktor {
    deployment {
        hostname = "localhost"
        hostname = ${?HOSTNAME}
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [ ApplicationKt.module ]
    }
}