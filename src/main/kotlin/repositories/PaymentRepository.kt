package repositories

import entities.PaymentEvents
import entities.Payments
import kotlinx.serialization.Serializable
import models.*
import org.jetbrains.exposed.v1.core.ResultRow
import org.jetbrains.exposed.v1.core.eq
import org.jetbrains.exposed.v1.jdbc.SchemaUtils
import org.jetbrains.exposed.v1.jdbc.insert
import org.jetbrains.exposed.v1.jdbc.selectAll
import org.jetbrains.exposed.v1.jdbc.transactions.transaction
import org.jetbrains.exposed.v1.jdbc.update
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import kotlin.time.Clock

class PaymentRepository {
    private val logger = LoggerFactory.getLogger(PaymentRepository::class.java)

    init {
        transaction {
            SchemaUtils.create(Payments, PaymentEvents)
        }
    }

    fun addPayment(
        paymentId: String,
        customerId: String,
        customerTokenId: String,
        amount: BigDecimal,
        currency: String,
        paymentMethod: PaymentMethod,
    ) {
        transaction {
            Payments.selectAll().where { Payments.paymentId eq id }.firstOrNull()?.let {
                throw AppException.ConflictException("Payment $paymentId already processed")
            }
            Payments.insert {
                it[Payments.paymentId] = paymentId
                it[Payments.customerId] = customerId
                it[Payments.customerTokenId] = customerTokenId
                it[Payments.amount] = amount
                it[Payments.currency] = currency
                it[Payments.paymentMethod] = paymentMethod.name
                it[Payments.createdAt] = Clock.System.now()
                it[Payments.updatedAt] = Clock.System.now()
            }
        }
    }

    fun addPaymentEvent(
        transactionId: String,
        paymentId: String,
        referenceId: String? = null,
        amount: BigDecimal,
        currency: String,
        paymentEventType: PaymentEventType,
        paymentEventStatus: PaymentEventStatus,
    ) {
        transaction {
            PaymentEvents.insert {
                it[PaymentEvents.transactionId] = transactionId
                it[PaymentEvents.paymentId] = paymentId
                it[PaymentEvents.referenceId] = referenceId
                it[PaymentEvents.paymentEventType] = paymentEventType.name
                it[PaymentEvents.paymentEventStatus] = paymentEventStatus.name
                it[PaymentEvents.amount] = amount
                it[PaymentEvents.currency] = currency
            }
        }
    }

    fun updatePaymentAuthorization(
        paymentId: String,
        paymentStatus: PaymentStatus,
        merchantStatus: String? = null,
        canonicalName: String? = null,
        cardType: String? = null,
        cardPrefix: String? = null,
        cardSuffix: String? = null,
        expMonth: String? = null,
        expYear: String? = null,
        paymentInstrumentId: String? = null,
        instrumentIdentifierId: String? = null,
        deviceFingerprint: String? = null,
    ) {
        transaction {
            Payments.update({ Payments.paymentId eq paymentId }) {
                it[Payments.paymentStatus] = paymentStatus.name
                it[Payments.merchantStatus] = merchantStatus
                it[Payments.updatedAt] = Clock.System.now()
                it[Payments.canonicalName] = canonicalName
                it[Payments.cardType] = cardType
                it[Payments.cardPrefix] = cardPrefix
                it[Payments.cardSuffix] = cardSuffix
                it[Payments.expMonth] = expMonth
                it[Payments.expYear] = expYear
                it[Payments.paymentInstrumentId] = paymentInstrumentId
                it[Payments.instrumentIdentifierId] = instrumentIdentifierId
                it[Payments.deviceFingerprint] = deviceFingerprint
            }
        }
    }

    fun addPaymentEventAuthorization(
        paymentId: String,
        transactionId: String,
        referenceId: String? = null,
        paymentEventType: PaymentEventType,
        paymentEventStatus: PaymentEventStatus,
        amount: BigDecimal,
        currency: String,
        errorCode: String? = null,
        errorMessage: String? = null,
        metadata: String? = null
    ) {
        transaction {
            PaymentEvents.insert {
                it[PaymentEvents.transactionId] = transactionId
                it[PaymentEvents.paymentId] = paymentId
                it[PaymentEvents.paymentEventType] = paymentEventType.name
                it[PaymentEvents.paymentEventStatus] = paymentEventStatus.name
                it[PaymentEvents.amount] = amount
                it[PaymentEvents.currency] = currency
                it[PaymentEvents.referenceId] = referenceId
                it[PaymentEvents.errorCode] = errorCode
                it[PaymentEvents.errorMessage] = errorMessage
                it[PaymentEvents.metadata] = metadata
                it[PaymentEvents.createdAt] = Clock.System.now()
            }
        }
    }


    fun updatePayment(
        paymentId: String,
        merchantStatus: String? = null,
        paymentStatus: PaymentStatus,
    ) {
        transaction {
            Payments.update({ Payments.paymentId eq paymentId }) {
                it[Payments.paymentStatus] = paymentStatus.name
                it[Payments.merchantStatus] = merchantStatus
                it[Payments.updatedAt] = Clock.System.now()
            }
        }
    }

    fun findPaymentById(id: String): Payment {
        return transaction {
            Payments.selectAll().where { Payments.paymentId eq id }.firstOrNull()?.toPayment()
        } ?: throw AppException.NotFoundException("Payment $id not found")
    }

    fun findPayments(): List<Payment> {
        return transaction { Payments.selectAll().map { it.toPayment() } }
    }

    fun fetchAllPaymentsWithEvents(): List<PaymentWithEvents> = transaction {
        val payments = Payments
            .selectAll()
            .map { it.toPayment() }

        val eventsByPaymentId = PaymentEvents
            .selectAll()
            .map { it.toPaymentEvent() }
            .groupBy { it.paymentId }

        payments.map { payment ->
            PaymentWithEvents(
                payment = payment,
                events = eventsByPaymentId[payment.paymentId] ?: emptyList()
            )
        }
    }

    fun findPaymentEventsByPaymentId(paymentId: String): List<PaymentEvent> {
        return transaction {
            PaymentEvents.selectAll().where { PaymentEvents.paymentId eq paymentId }.map { it.toPaymentEvent() }
        }
    }

    private fun ResultRow.toPayment(): Payment {
        return Payment(
            paymentId = this[Payments.paymentId],
            customerId = this[Payments.customerId],
            customerTokenId = this[Payments.customerTokenId],
            amount = this[Payments.amount].toPlainString(),
            currency = this[Payments.currency],
            paymentMethod = PaymentMethod.valueOf(this[Payments.paymentMethod]),
            paymentStatus = PaymentStatus.valueOf(this[Payments.paymentStatus]),
            merchantStatus = this[Payments.merchantStatus],
            canonicalName = this[Payments.canonicalName],
            cardType = this[Payments.cardType],
            cardPrefix = this[Payments.cardPrefix],
            cardSuffix = this[Payments.cardSuffix],
            expMonth = this[Payments.expMonth],
            expYear = this[Payments.expYear],
            paymentInstrumentId = this[Payments.paymentInstrumentId],
            instrumentIdentifierId = this[Payments.instrumentIdentifierId],
            deviceFingerprint = this[Payments.deviceFingerprint],
            createdAt = this[Payments.createdAt],
            updatedAt = this[Payments.updatedAt]
        )
    }

    private fun ResultRow.toPaymentEvent(): PaymentEvent {
        return PaymentEvent(
            transactionId = this[PaymentEvents.transactionId],
            paymentId = this[PaymentEvents.paymentId],
            referenceId = this[PaymentEvents.referenceId],
            paymentEventStatus = PaymentEventStatus.valueOf(this[PaymentEvents.paymentEventStatus]),
            paymentEventType = PaymentEventType.valueOf(this[PaymentEvents.paymentEventType]),
            amount = this[PaymentEvents.amount].toPlainString(),
            currency = this[PaymentEvents.currency],
            errorCode = this[PaymentEvents.errorCode],
            errorMessage = this[PaymentEvents.errorMessage],
            createdAt = this[PaymentEvents.createdAt],
            metadata = this[PaymentEvents.metadata],
        )
    }
}

@Serializable
data class PaymentWithEvents(
    val payment: Payment,
    val events: List<PaymentEvent>
)