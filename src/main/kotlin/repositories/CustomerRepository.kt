package repositories

import entities.Customers
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import models.AccountStatus
import models.AppException
import org.jetbrains.exposed.v1.core.ResultRow
import org.jetbrains.exposed.v1.core.eq
import org.jetbrains.exposed.v1.jdbc.SchemaUtils
import org.jetbrains.exposed.v1.jdbc.deleteWhere
import org.jetbrains.exposed.v1.jdbc.insert
import org.jetbrains.exposed.v1.jdbc.selectAll
import org.jetbrains.exposed.v1.jdbc.transactions.transaction
import org.slf4j.LoggerFactory
import kotlin.time.Instant

class CustomerRepository {
    private val logger = LoggerFactory.getLogger(CustomerRepository::class.java)

    init {
        transaction {
            SchemaUtils.create(Customers)
        }
    }

    fun saveCustomer(id: String, processorId: String): DbCustomer {
        return transaction {
            Customers.insert {
                it[this.id] = id
                it[this.processorId] = processorId
            }
            Customers.selectAll().where { Customers.id eq id }.first().toCustomer()
        }
    }

    fun findCustomerById(id: String): DbCustomer {
        return transaction {
            val customer = Customers.selectAll().where { Customers.id eq id }.firstOrNull()?.toCustomer() ?: throw AppException.NotFoundException("Customer $id not found")
            if (customer.status == AccountStatus.LOCK)
                throw AppException.Forbidden("Customer account is locked")
            customer
        }
    }

    fun deleteCustomerById(id: String): Boolean {
        return transaction {
            Customers.deleteWhere { Customers.id eq id } > 0
        }
    }

    private fun ResultRow.toCustomer(): DbCustomer {
        return DbCustomer(
            id = this[Customers.id],
            processorId = this[Customers.processorId],
            status = AccountStatus.valueOf(this[Customers.status]),
            createdAt = this[Customers.createdAt]
        )
    }

    @Serializable
    data class DbCustomer(
        val id: String,
        val processorId: String,
        val status: AccountStatus,
        @Contextual val createdAt: Instant
    )
}