import com.typesafe.config.Config
import com.typesafe.config.ConfigFactory
import database.DatabaseFactory
import io.ktor.server.application.*
import io.ktor.server.cio.*
import io.ktor.server.engine.*
import kotlinx.coroutines.launch
import plugins.*
import repositories.CustomerRepository
import repositories.PaymentRepository
import services.CybersourceService
import usecases.CustomerUseCase

fun main() {
    val config: Config = ConfigFactory.load()
    val serverPort: Int = config.getInt("ktor.deployment.port")
    val serverHostname: String = config.getString("ktor.deployment.hostname")

    embeddedServer(factory = CIO, port = serverPort, host = serverHostname, module = Application::module, watchPaths = listOf("classes", "resources")).start(wait = true)
}

fun Application.module() {
    DatabaseFactory.connect()

    val paymentRepository = PaymentRepository()
    val cybersourceService = CybersourceService(paymentRepository = paymentRepository)
    val customerRepository = CustomerRepository()
    val customerUseCase = CustomerUseCase(customerRepository = customerRepository, cybersourceService = cybersourceService)

    configureHealth()
    configureMonitoring()
    configureSerialization()
    configureTemplating()
    configureSecurity()
    configureApi()
    configureRouting(cybersourceService = cybersourceService, paymentRepository = paymentRepository, customerRepository = customerRepository, customerUseCase = customerUseCase)
}