package models

import kotlinx.serialization.Serializable
import utils.Config

object Request {
    @Serializable
    data class PaymentSession(val paymentMethod: String, val deviceType: String) {

        fun sanitize() = PaymentSession(paymentMethod.trim(), deviceType.trim())

        fun validate() {
            if (!PaymentMethod.entries.toTypedArray().map { it.name }.contains(paymentMethod))
                throw AppException.BadRequestException("Payment method $paymentMethod not supported")

            if (!DeviceType.entries.toTypedArray().map { it.name }.contains(deviceType))
                throw AppException.BadRequestException("Invalid device type $deviceType")
        }
    }

    @Serializable
    data class CreditCardDetail(val paymentData: String, val storeInVault: Boolean) {
        fun sanitize() = CreditCardDetail(paymentData.trim(), storeInVault)
    }

    @Serializable
    data class VaultDetails(val paymentInstrumentId: String)

    @Serializable
    data class Authorize(
        val amount: String,
        val currency: String,
        val customerDetail: CustomerDetail,
        val creditCardDetail: CreditCardDetail? = null,
        val vaultDetails: VaultDetails? = null,
        val billingAddress: BillingAddress? = null,
        val deviceFingerprint: DeviceFingerprint? = null
    ) {
        fun sanitize() = Authorize(amount = amount.trim(), currency = currency.trim(), customerDetail = customerDetail.sanitize(), creditCardDetail = creditCardDetail?.sanitize(), vaultDetails = vaultDetails, billingAddress = billingAddress, deviceFingerprint = deviceFingerprint)

        fun validate() {
            if (!Config.isAmountValid(amount)) throw AppException.BadRequestException("Amount is not valid.")
            if (!Config.currencies.contains(currency)) throw AppException.BadRequestException("Currency is not valid")
        }
    }

    @Serializable
    data class DefaultVault(val customerTokenId: String)

    @Serializable
    data class PayPalDetails(val customerTokenId: String, val payerId: String, val orderId: String)

    @Serializable
    data class ApplePayDetails(val customerTokenId: String, val paymentData: String)

    @Serializable
    data class GooglePayDetails(val customerTokenId: String, val paymentData: String)

    @Serializable
    data class CustomerDetail(
        val id: String,
        val email: String,
        val phone: String? = null,
        val firstName: String,
        val lastName: String
    ) {
        fun sanitize() = CustomerDetail(id.trim(), email.trim(), phone?.trim(), firstName.trim(), lastName.trim())
        fun validate() {
            if (id.isBlank()) throw AppException.BadRequestException("Customer ID is required")
            if (email.isBlank()) throw AppException.BadRequestException("Email is required")
            if (firstName.isBlank()) throw AppException.BadRequestException("First name is required")
            if (lastName.isBlank()) throw AppException.BadRequestException("Last name is required")
        }
    }

    @Serializable
    data class CreditCardCapture(val paymentId: String) {
        fun sanitize() = CreditCardCapture(paymentId.trim())
        fun validate() {
            if (paymentId.isBlank()) throw AppException.BadRequestException("Payment ID is required")
        }
    }

    @Serializable
    data class CreditCardReverse(val paymentId: String, val reason: String) {
        fun sanitize() = CreditCardReverse(paymentId.trim(), reason.trim())
        fun validate() {
            if (paymentId.isBlank()) throw AppException.BadRequestException("Payment ID is required")
            if (reason.isBlank()) throw AppException.BadRequestException("Reason is required")
        }
    }

    @Serializable
    data class CreditCardVoid(val paymentId: String) {
        fun sanitize() = CreditCardVoid(paymentId.trim())
        fun validate() {
            if (paymentId.isBlank()) throw AppException.BadRequestException("Payment ID is required")
        }
    }

    @Serializable
    data class UpdateDefaultPaymentInstrument(val isDefault: Boolean)

    @Serializable
    data class UpdatePaymentInstrument(val billingAddress: BillingAddress)
}