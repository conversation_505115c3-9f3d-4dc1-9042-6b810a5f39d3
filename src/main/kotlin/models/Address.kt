package models

import kotlinx.serialization.Serializable

/**
 *
 * @param iso2          US              required or N/A
 * @param state         IL
 * @param city          chicago         required or N/A
 * @param zipCode       60622           required of US
 * @param streetAddress E Nain St       required or N/A
 * @param extendedAddress Apt 1234
 */
@Serializable
data class Address(
    val iso2: String,
    val state: String,
    val city: String,
    val zipCode: String,
    val streetAddress: String,
    val extendedAddress: String
)

