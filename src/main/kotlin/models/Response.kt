package models

import kotlinx.serialization.Serializable

object Response {

    @Serializable
    data class PaymentSession(
        val paymentId: String,
        val paymentSignature: String,
        val deviceType: String,
        val paymentMethod: String,
        val organizationId: String,
        val merchantId: String,
    )

    @Serializable
    data class Authorized(
        val date: String,
        val paymentId: String,
        val amount: String,
        val currency: String,
        val canonicalName: String,
        val cardType: String
    )

    @Serializable
    data class Captured(
        val date: String,
        val paymentId: String,
        val amount: String,
        val currency: String,
        val canonicalName: String,
        val cardType: String
    )

    @Serializable
    data class Reversed(val date: String, val paymentId: String, val amount: String, val currency: String)

    @Serializable
    data class Voided(val date: String, val paymentId: String, val amount: String, val currency: String)

    @Serializable
    data class Payment(
        val paymentId: String,
        val customerId: String,
        val amount: String,
        val currency: String,
        val status: String,
        val paymentMethod: String,
        val createdAt: String,
        val canonicalName: String?,
        val cardType: String?,
        val events: List<PaymentEvent>
    )

    @Serializable
    data class PaymentEvent(
        val referenceId: String?,
        val paymentEventType: String,
        val paymentEventStatus: String,
        val amount: String,
        val currency: String,
        val createdAt: String,
        val errorCode: String?,
        val errorMessage: String?,
        val metadata: String?,
    )
}