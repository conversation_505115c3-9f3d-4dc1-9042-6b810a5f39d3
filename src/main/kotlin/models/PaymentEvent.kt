package models

import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import kotlin.time.Instant

@Serializable
data class PaymentEvent(
    val transactionId: String,
    val referenceId: String?,
    val paymentId: String,
    val paymentEventStatus: PaymentEventStatus,
    val paymentEventType: PaymentEventType,
    val amount: String,
    val currency: String,
    val errorCode: String?,
    val errorMessage: String?,
    val metadata: String?,
    @Contextual val createdAt: Instant
)