package models

sealed class AppException(message: String) : RuntimeException(message) {
    class NotFoundException(message: String = "Customer not found") : AppException(message)
    class BadRequestException(message: String) : AppException(message)
    class ConflictException(message: String) : AppException(message)
    class Forbidden(message: String) : AppException(message)
    class InternalServerErrorException(message: String) : AppException(message)
}