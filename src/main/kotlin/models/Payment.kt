package models

import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import kotlin.time.Instant

@Serializable
data class Payment(
    val paymentId: String,
    val customerId: String,
    val customerTokenId: String,
    val amount: String,
    val currency: String,
    val paymentStatus: PaymentStatus,
    val merchantStatus: String?,
    val paymentMethod: PaymentMethod,
    @Contextual val createdAt: Instant,
    @Contextual val updatedAt: Instant,
    val canonicalName: String?,
    val cardType: String?,
    val cardPrefix: String?,
    val cardSuffix: String?,
    val expMonth: String?,
    val expYear: String?,
    val paymentInstrumentId: String?,
    val instrumentIdentifierId: String?,
    val deviceFingerprint: String?
)
