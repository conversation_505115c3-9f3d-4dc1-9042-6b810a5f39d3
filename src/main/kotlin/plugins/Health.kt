package plugins

import database.DatabaseFactory
import io.github.smiley4.ktoropenapi.get
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.config.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

fun Application.configureHealth() {
    val log = LoggerFactory.getLogger("HealthCheck")
    val appName = environment.config.tryGetString("app.name") ?: "App"

    routing {
        get("/healthz", {
            tags = listOf("Health")
            description = "Health check"
            operationId = "healthCheck"
            response {
                HttpStatusCode.OK to {
                    description = "Application is healthy"
                }
            }
        }) {
            call.respondText("$appName OK", ContentType.Text.Plain, HttpStatusCode.OK)
        }

        get("/ready", {
            tags = listOf("Health")
            description = "Ready check"
            operationId = "readyCheck"
            response {
                HttpStatusCode.OK to {
                    description = "Application is ready to receive traffic"
                }
            }
        }) {
            if (DatabaseFactory.isAppReady()) {
                call.respondText("$appName READY", ContentType.Text.Plain, HttpStatusCode.OK)
            } else {
                log.warn("$appName is not ready")
                call.respondText("$appName NOT READY", ContentType.Text.Plain, HttpStatusCode.ServiceUnavailable)
            }
        }
    }
}