package plugins

import freemarker.cache.ClassTemplateLoader
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.freemarker.*
import io.ktor.server.http.content.*
import io.ktor.server.response.*
import io.ktor.server.routing.*

fun Application.configureTemplating() {
    install(FreeMarker) {
        templateLoader = ClassTemplateLoader(this::class.java.classLoader, "templates")
    }

    routing {
        route({ hidden = true }) {
            staticResources("/assets", "assets")
        }

        get("/robots.txt", {
            description = "No indexing of this site"
            hidden = true
        }) {
            call.respondText(
                """
            User-agent: *
            Disallow: /
            """.trimIndent(),
                ContentType.Text.Plain
            )
        }
    }
}