package plugins

import io.ktor.server.application.*
import io.ktor.server.plugins.calllogging.*
import io.ktor.server.request.*
import org.slf4j.event.Level

fun Application.configureMonitoring() {
    install(CallLogging) {
        level = Level.INFO

        filter { call ->
            !call.request.path().startsWith("/assets") && call.request.path() != "/healthz" && call.request.path() != "/ready" && call.request.path() != "/swagger" && call.request.path() != "/openapi.yaml"
        }

        format { call ->
            val status = call.response.status()?.value
            val httpMethod = call.request.httpMethod.value
            val host = call.request.host()
            val path = call.request.path()

            val forwardFor = call.request.headers["X-Forwarded-For"]

            "Status: $status, Method: $httpMethod, Host: $host, IP: $forwardFor,  Path: $path"
        }
    }
}