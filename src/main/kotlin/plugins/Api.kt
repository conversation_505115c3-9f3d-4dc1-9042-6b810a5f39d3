package plugins

import io.github.smiley4.ktoropenapi.OpenApi
import io.github.smiley4.ktoropenapi.config.OutputFormat
import io.github.smiley4.ktoropenapi.config.SchemaGenerator
import io.github.smiley4.ktoropenapi.openApi
import io.github.smiley4.ktorswaggerui.swaggerUI
import io.ktor.server.application.*
import io.ktor.server.routing.*

fun Application.configureApi() {
    install(OpenApi) {
        outputFormat = OutputFormat.YAML
        tags {
            tag("Health") {
                description = "Health check"
            }
            tag("Customer") {
                description = "Customer operations"
            }
            tag("Payment") {
                description = "Payment operations"
            }
        }
        info {
            version = "1.0.0"
            title = "Purchase API"
            contact {
                email = "<EMAIL>"
            }
        }

        server {
            url = "http://purchase-api"
            description = "Production server"
        }
        server {
            url = "http://purchase-api-"
            description = "Staging server"
        }
        server {
            url = "http://localhost:5010"
            description = "Local server"
        }
        schemas {
            generator = SchemaGenerator.kotlinx {
                explicitNullTypes = false
                title = null
            }
        }
    }

    routing {
        route("openapi.yaml") {
            openApi()
        }

        route("swagger") {
            swaggerUI("/openapi.yaml")
        }
    }
}

