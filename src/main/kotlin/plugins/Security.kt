package plugins

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.response.*
import models.AppError
import models.AppErrorCode
import models.AppException
import org.slf4j.LoggerFactory

fun Application.configureSecurity() {
    LoggerFactory.getLogger("configureRouting")

    install(StatusPages) {
        exception<AppException> { call, cause ->
            when (cause) {
                is AppException.NotFoundException -> call.respond(
                    HttpStatusCode.NotFound,
                    AppError(AppErrorCode.NOT_FOUND.name, cause.message ?: "")
                )

                is AppException.BadRequestException -> call.respond(
                    HttpStatusCode.BadRequest,
                    AppError(AppErrorCode.BAD_REQUEST.name, cause.message ?: "")
                )

                is AppException.ConflictException -> call.respond(
                    HttpStatusCode.Conflict,
                    AppError(AppErrorCode.CONFLICT.name, cause.message ?: "")
                )

                is AppException.Forbidden -> call.respond(
                    HttpStatusCode.Forbidden,
                    AppError(AppErrorCode.FORBIDDEN.name, cause.message ?: "")
                )

                is AppException.InternalServerErrorException -> call.respond(
                    HttpStatusCode.InternalServerError,
                    AppError(AppErrorCode.INTERNAL_SERVER_ERROR.name, cause.message ?: "")
                )
            }
        }

        exception<io.ktor.server.plugins.BadRequestException> { call, cause ->
            cause.printStackTrace()
            call.respond(HttpStatusCode.BadRequest, mapOf(AppErrorCode.BAD_REQUEST to "One or more fields are invalid"))
        }

        exception<Throwable> { call, cause ->
            cause.printStackTrace()
            call.respond(
                HttpStatusCode.InternalServerError,
                mapOf(AppErrorCode.INTERNAL_SERVER_ERROR to "Unable to complete your request. Try again later")
            )
        }

        exception<IllegalStateException> { call, cause ->
            cause.printStackTrace()
            call.respond(
                HttpStatusCode.InternalServerError,
                mapOf(AppErrorCode.INTERNAL_SERVER_ERROR to "Unable to complete your request. Try again later")
            )
        }
    }

}