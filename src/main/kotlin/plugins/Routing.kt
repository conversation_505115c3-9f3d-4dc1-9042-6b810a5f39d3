package plugins

import io.github.smiley4.ktoropenapi.delete
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.patch
import io.github.smiley4.ktoropenapi.post
import io.github.smiley4.ktoropenapi.put
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import models.AppError
import models.AppException
import models.Customer
import models.PaymentInstrument
import models.PaymentMethod
import models.PaymentStatus
import models.Request
import models.Response
import repositories.CustomerRepository
import repositories.PaymentRepository
import services.CybersourceService
import usecases.CustomerUseCase
import utils.toIso8601Seconds
import java.math.BigDecimal

fun Application.configureRouting(
    cybersourceService: CybersourceService,
    customerRepository: CustomerRepository,
    customerUseCase: CustomerUseCase,
    paymentRepository: PaymentRepository
) {
    routing {
        route("/api/v1") {
            route("/customers") {
                route("/{id}") {
                    get({
                        tags = listOf("Payment")
                        operationId = "getCustomer"
                        description = "Get customer by id"
                        request {
                            pathParameter<String>("id") {
                                description = "Customer id"
                                required = true
                            }
                        }
                        response {
                            HttpStatusCode.OK to {
                                description = "Customer found"
                                body<Customer>()
                            }
                            HttpStatusCode.Forbidden to {
                                description = "Customer account is locked"
                                body<AppError>()
                            }
                            HttpStatusCode.NotFound to {
                                description = "Customer not found"
                            }
                            HttpStatusCode.InternalServerError to {
                                description = "Internal server error"
                                body<AppError>()
                            }
                        }
                    }) {
                        val customerId = call.parameters["id"]!!

                        val customer = customerRepository.findCustomerById(id = customerId)
                        val cybersourceCustomer: Customer = cybersourceService.getCustomer(id = customer.processorId)
                        call.respond<Customer>(cybersourceCustomer)
                    }

                    delete({
                        tags = listOf("Payment")
                        operationId = "deleteCustomer"
                        description = "Delete a customer by id"
                        request {
                            pathParameter<String>("id") {
                                description = "The customer id"
                                required = true
                            }
                        }
                        response {
                            HttpStatusCode.NoContent to {
                                description = "Customer deleted"
                            }
                            HttpStatusCode.NotFound to {
                                description = "Customer not found"
                                body<AppError>()
                            }
                            HttpStatusCode.Forbidden to {
                                description = "Customer account is locked"
                                body<AppError>()
                            }
                            HttpStatusCode.Conflict to {
                                description = "Duplicate transaction"
                                body<AppError>()
                            }
                            HttpStatusCode.InternalServerError to {
                                description = "Internal server error"
                                body<AppError>()
                            }
                        }
                    }) {
                        val customerId = call.parameters["id"]!!

                        val customer = customerRepository.findCustomerById(id = customerId)

                        cybersourceService.deleteCustomer(id = customer.processorId)
                        customerRepository.deleteCustomerById(id = customerId)
                        call.respond(HttpStatusCode.NoContent)
                    }

                    route("/payment-instruments/{paymentInstrumentId}") {
                        put({
                            tags = listOf("Payment")
                            operationId = "updatePaymentInstrument"
                            description = "Update a payment instrument billing address"
                            request {
                                pathParameter<String>("id") {
                                    description = "The customer id"
                                    required = true
                                }
                                pathParameter<String>("paymentInstrumentId") {
                                    description = "The payment instrument id"
                                    required = true
                                }
                                body<Request.UpdatePaymentInstrument> {
                                    description = "Payment instrument update request"
                                }
                            }
                            response {
                                HttpStatusCode.OK to {
                                    description = "Payment instrument updated"
                                    body<List<PaymentInstrument>>()
                                }
                                HttpStatusCode.Forbidden to {
                                    description = "Customer account is locked"
                                    body<AppError>()
                                }
                                HttpStatusCode.NotFound to {
                                    description = "Payment instrument not found"
                                    body<AppError>()
                                }
                                HttpStatusCode.BadRequest to {
                                    description = "Invalid request data"
                                    body<AppError>()
                                }
                                HttpStatusCode.Conflict to {
                                    description = "Duplicate transaction"
                                    body<AppError>()
                                }
                                HttpStatusCode.InternalServerError to {
                                    description = "Internal server error"
                                    body<AppError>()
                                }
                            }
                        }) {
                            val customerTokenId = call.parameters["customerTokenId"]!!
                            val paymentInstrumentId = call.parameters["paymentInstrumentId"]!!

                            val body = call.receive<Request.UpdatePaymentInstrument>()
                            val response: List<PaymentInstrument> = cybersourceService.updatePaymentInstrument(billingAddress = body.billingAddress, paymentInstrumentId = paymentInstrumentId, customerTokenId = customerTokenId)
                            call.respond<List<PaymentInstrument>>(response)
                        }

                        patch({
                            tags = listOf("Payment")
                            operationId = "updateDefaultPaymentInstrument"
                            description = "Update a payment instrument as default"
                            request {
                                pathParameter<String>("id") {
                                    description = "The customer id"
                                    required = true
                                }
                                pathParameter<String>("paymentInstrumentId") {
                                    description = "The payment instrument id"
                                    required = true
                                }
                                body<Request.UpdateDefaultPaymentInstrument> {
                                    description = "Payment instrument update request"
                                }
                            }
                            response {
                                HttpStatusCode.OK to {
                                    description = "Payment instrument updated"
                                    body<List<PaymentInstrument>>()
                                }
                                HttpStatusCode.Forbidden to {
                                    description = "Customer account is locked"
                                    body<AppError>()
                                }
                                HttpStatusCode.NotFound to {
                                    description = "Payment instrument not found"
                                    body<AppError>()
                                }
                                HttpStatusCode.BadRequest to {
                                    description = "Invalid request data"
                                    body<AppError>()
                                }
                                HttpStatusCode.Conflict to {
                                    description = "Duplicate transaction"
                                    body<AppError>()
                                }
                                HttpStatusCode.InternalServerError to {
                                    description = "Internal server error"
                                    body<AppError>()
                                }
                            }
                        }) {
                            val customerTokenId = call.parameters["customerTokenId"]!!
                            val paymentInstrumentId = call.parameters["paymentInstrumentId"]!!

                            val body = call.receive<Request.UpdateDefaultPaymentInstrument>()

                            val isDefault = body.isDefault
                            val response: List<PaymentInstrument> = cybersourceService.updateDefaultPaymentInstrument(customerTokenId = customerTokenId, paymentInstrumentId = paymentInstrumentId, isDefault = isDefault)
                            call.respond<List<PaymentInstrument>>(response)
                        }

                        delete({
                            tags = listOf("Payment")
                            operationId = "deletePaymentInstrument"
                            description = "Delete a payment instrument by id"
                            request {
                                pathParameter<String>("id") {
                                    description = "The customer id"
                                    required = true
                                }
                                pathParameter<String>("paymentInstrumentId") {
                                    description = "The payment instrument id"
                                    required = true
                                }
                            }
                            response {
                                HttpStatusCode.NoContent to {
                                    description = "Customer deleted"
                                }
                                HttpStatusCode.Forbidden to {
                                    description = "Customer account is locked"
                                    body<AppError>()
                                }
                                HttpStatusCode.NotFound to {
                                    description = "Payment instrument not found"
                                    body<AppError>()
                                }
                                HttpStatusCode.BadRequest to {
                                    description = "Invalid request data"
                                    body<AppError>()
                                }
                                HttpStatusCode.Conflict to {
                                    description = "Duplicate transaction"
                                    body<AppError>()
                                }
                                HttpStatusCode.InternalServerError to {
                                    description = "Internal server error"
                                    body<AppError>()
                                }
                            }
                        }) {
                            val customerTokenId = call.parameters["customerTokenId"]!!
                            val paymentInstrumentId = call.parameters["paymentInstrumentId"]!!

                            cybersourceService.deletePaymentInstrument(customerTokenId = customerTokenId, paymentInstrumentId = paymentInstrumentId)
                            call.respond(HttpStatusCode.NoContent)
                        }
                    }
                }
            }

            post("/payment-sessions", {
                tags = listOf("Payment")
                operationId = "createPaymentSession"
                description = "Create a payment session"
                request {
                    body<Request.PaymentSession> {
                        description = "Payment session request"
                    }
                }
                response {
                    HttpStatusCode.OK to {
                        description = "Payment session created"
                        body<Response.PaymentSession>()
                    }
                    HttpStatusCode.BadRequest to {
                        description = "Invalid request"
                        body<AppError>()
                    }
                    HttpStatusCode.InternalServerError to {
                        description = "Internal server error"
                        body<AppError>()
                    }
                }
            }) {
                try {
                    val body = call.receive<Request.PaymentSession>().sanitize()
                    body.validate()

                    val response: Response.PaymentSession = cybersourceService.generateSession(deviceType = body.deviceType, paymentMethod = PaymentMethod.valueOf(body.paymentMethod))
                    call.respond<Response.PaymentSession>(response)
                } catch (e: Throwable) {
                    call.respond(HttpStatusCode.BadRequest, mapOf("error" to e.message))
                }

            }

            route("/payments") {
                get({
                    tags = listOf("Payment")
                    operationId = "getPayments"
                    description = "Get all payments"
                    response {
                        HttpStatusCode.OK to {
                            description = "Payments found"
                            body<List<Response.Payment>>()
                        }

                        HttpStatusCode.InternalServerError to {
                            description = "Internal server error"
                            body<AppError>()
                        }
                    }
                }) {
                    val payments: List<Response.Payment> = paymentRepository.fetchAllPaymentsWithEvents().map { data ->
                        val payment = data.payment
                        val events = data.events.map { Response.PaymentEvent(referenceId = it.referenceId, paymentEventStatus = it.paymentEventStatus.name, paymentEventType = it.paymentEventType.name, amount = it.amount, currency = it.currency, errorCode = it.errorCode, errorMessage = it.errorMessage, metadata = it.metadata, createdAt = it.createdAt.toIso8601Seconds()) }.sortedBy { it.createdAt }
                        Response.Payment(paymentId = payment.paymentId, customerId = payment.customerId, amount = payment.amount, currency = payment.currency, status = payment.paymentStatus.name, canonicalName = payment.canonicalName, paymentMethod = payment.paymentMethod.name, cardType = payment.cardType, createdAt = payment.createdAt.toIso8601Seconds(), events = events) }.sortedBy { it.createdAt }
                    call.respond<List<Response.Payment>>(payments)
                }

                route("/{id}") {
                    get({
                        tags = listOf("Payment")
                        operationId = "getPayment"
                        description = "Get payment by id"
                        request {
                            pathParameter<String>("id") {
                                description = "Payment id"
                                required = true
                            }
                        }
                        response {
                            HttpStatusCode.OK to {
                                description = "Payment found"
                                body<Response.Payment>()
                            }
                            HttpStatusCode.NotFound to {
                                description = "Payment not found"
                                body<AppError>()
                            }
                            HttpStatusCode.InternalServerError to {
                                description = "Internal server error"
                                body<AppError>()
                            }
                        }
                    }) {
                        val paymentId = call.parameters["id"]!!
                        val payment = paymentRepository.findPaymentById(id = paymentId)
                        val paymentEvents = paymentRepository.findPaymentEventsByPaymentId(paymentId = paymentId).map { Response.PaymentEvent(referenceId = it.referenceId, paymentEventStatus = it.paymentEventStatus.name, paymentEventType = it.paymentEventType.name, amount = it.amount, currency = it.currency, errorCode = it.errorCode, errorMessage = it.errorMessage, metadata = it.metadata, createdAt = it.createdAt.toIso8601Seconds()) }.sortedBy { it.createdAt }

                        val response = Response.Payment(paymentId = payment.paymentId, customerId = payment.customerId, amount = payment.amount, currency = payment.currency, status = payment.paymentStatus.name, canonicalName = payment.canonicalName, paymentMethod = payment.paymentMethod.name, cardType = payment.cardType, createdAt = payment.createdAt.toIso8601Seconds(), events = paymentEvents)
                        call.respond(response)

                    }

                    post("/authorize", {
                        tags = listOf("Payment")
                        operationId = "authorizePayment"
                        description = "Authorize a payment"
                        request {
                            pathParameter<String>("id") {
                                description = "The payment id"
                                required = true
                            }
                            body<Request.Authorize> {
                                description = "Payment authorize request"
                            }
                        }
                        response {
                            HttpStatusCode.OK to {
                                description = "Payment authorized"
                                body<Response.Authorized>()
                            }
                            HttpStatusCode.BadRequest to {
                                description = "Invalid request"
                                body<AppError>()
                            }
                            HttpStatusCode.Forbidden to {
                                description = "Payment is not authorized for this customer"
                                body<AppError>()
                            }
                            HttpStatusCode.NotFound to {
                                description = "Payment not found"
                                body<AppError>()
                            }
                            HttpStatusCode.Conflict to {
                                description = "Payment already authorized"
                                body<AppError>()
                            }
                            HttpStatusCode.InternalServerError to {
                                description = "Internal server error"
                                body<AppError>()
                            }
                        }
                    }) {
                        val paymentId = call.parameters["id"]!!
                        val body = call.receive<Request.Authorize>().sanitize()
                        body.validate()

                        val paymentSession = cybersourceService.getPaymentSession(paymentId = paymentId)
                        val customerTokenId = customerUseCase.getCustomerTokenId(customerId = body.customerDetail.id, customerEmail = body.customerDetail.email)

                        when (PaymentMethod.valueOf(paymentSession.paymentMethod)) {
                            PaymentMethod.CREDIT_CARD -> {
                                val cardDetail = body.creditCardDetail ?: throw AppException.BadRequestException("Credit card details are required for credit card payments")
                                val billingAddress = body.billingAddress ?: throw AppException.BadRequestException("Billing address is required for credit card payments")
                                val response: Response.Authorized = cybersourceService.authorizeWithCreditCard(paymentId = paymentId, customerId = body.customerDetail.id, customerTokenId = customerTokenId, customerEmail = body.customerDetail.email, customerPhone = body.customerDetail.phone, paymentData = cardDetail.paymentData, amount = BigDecimal(body.amount), currency = body.currency, storeInVault = body.creditCardDetail.storeInVault, billingAddress = billingAddress, deviceFingerprint = body.deviceFingerprint)
                                call.respond(response)
                            }
                            PaymentMethod.VAULT -> {
                                val vaultDetails = body.vaultDetails ?: throw AppException.BadRequestException("Vault details are required for vault payments")
                                val response = cybersourceService.authorizeWithCreditCard(paymentId = paymentId, customerId = body.customerDetail.id, customerTokenId = customerTokenId, customerEmail = body.customerDetail.email, customerPhone = body.customerDetail.phone, paymentInstrumentId = vaultDetails.paymentInstrumentId, amount = BigDecimal(body.amount), currency = body.currency, storeInVault = false, deviceFingerprint = body.deviceFingerprint)
                                call.respond(response)
                            }
                            PaymentMethod.DEFAULT_VAULT -> {
                                val response = cybersourceService.authorizeWithCreditCard(paymentId = paymentId, customerId = body.customerDetail.id, customerTokenId = customerTokenId, customerEmail = body.customerDetail.email, customerPhone = body.customerDetail.phone, amount = BigDecimal(body.amount), currency = body.currency, storeInVault = false, deviceFingerprint = body.deviceFingerprint)
                                call.respond(response)
                            }
                            PaymentMethod.PAYPAL -> TODO()
                            PaymentMethod.GOOGLE_PAY -> TODO()
                            PaymentMethod.APPLE_PAY -> TODO()
                        }
                    }

                    post("/capture", {
                        tags = listOf("Payment")
                        operationId = "capturePayment"
                        description = "Capture a payment"
                        request {
                            pathParameter<String>("id") {
                                description = "The payment id"
                                required = true
                            }
                            body<Request.CreditCardCapture> {
                                description = "Payment capture request"
                            }
                        }
                        response {
                            HttpStatusCode.OK to {
                                description = "Payment captured"
                                body<Response.Captured>()
                            }
                            HttpStatusCode.BadRequest to {
                                description = "Invalid request"
                                body<AppError>()
                            }
                            HttpStatusCode.NotFound to {
                                description = "Payment not found"
                                body<AppError>()
                            }
                            HttpStatusCode.Conflict to {
                                description = "Payment already captured"
                                body<AppError>()
                            }
                            HttpStatusCode.InternalServerError to {
                                description = "Internal server error"
                                body<AppError>()
                            }
                        }
                    }) {
                        val body = call.receive<Request.CreditCardCapture>()
                        val response: Response.Captured = cybersourceService.captureAuthorization(paymentId = body.paymentId)
                        call.respond<Response.Captured>(response)
                    }

                    post("/reverse", {
                        tags = listOf("Payment")
                        operationId = "reversePayment"
                        description = "Reverse a payment"
                        request {
                            pathParameter<String>("id") {
                                description = "The payment id"
                                required = true
                            }
                            body<Request.CreditCardReverse> {
                                description = "Payment reverse request"
                            }
                        }
                        response {
                            HttpStatusCode.OK to {
                                description = "Payment reversed"
                                body<Response.Reversed>()
                            }
                            HttpStatusCode.BadRequest to {
                                description = "Invalid request"
                                body<AppError>()
                            }
                            HttpStatusCode.NotFound to {
                                description = "Payment not found"
                                body<AppError>()
                            }
                            HttpStatusCode.Conflict to {
                                description = "Payment already reversed"
                                body<AppError>()
                            }
                            HttpStatusCode.InternalServerError to {
                                description = "Internal server error"
                                body<AppError>()
                            }
                        }
                    }) {
                        val body = call.receive<Request.CreditCardReverse>()

                        val response: Response.Reversed = cybersourceService.reverseAuthorization(paymentId = body.paymentId, reason = body.reason)
                        call.respond<Response.Reversed>(response)
                    }

                    post("/void", {
                        tags = listOf("Payment")
                        operationId = "voidPayment"
                        description = "Void a payment"
                        request {
                            pathParameter<String>("id") {
                                description = "The payment id"
                                required = true
                            }
                            body<Request.CreditCardVoid> {
                                description = "Payment void request"
                            }
                        }
                        response {
                            HttpStatusCode.OK to {
                                description = "Payment voided"
                                body<Response.Voided>()
                            }
                            HttpStatusCode.BadRequest to {
                                description = "Invalid request"
                                body<AppError>()
                            }
                            HttpStatusCode.NotFound to {
                                description = "Payment not found"
                                body<AppError>()
                            }
                            HttpStatusCode.Conflict to {
                                description = "Payment already void"
                                body<AppError>()
                            }
                            HttpStatusCode.InternalServerError to {
                                description = "Internal server error"
                                body<AppError>()
                            }
                        }
                    }) {
                        val body = call.receive<Request.CreditCardVoid>()

                        val response: Response.Voided = cybersourceService.voidCapture(paymentId = body.paymentId)
                        call.respond<Response.Voided>(response)
                    }
                }
            }
        }
    }
}