package utils

import Api.MicroformIntegrationApi
import Invokers.ApiClient
import Invokers.ApiException
import Model.GenerateCaptureContextRequest
import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.cybersource.authsdk.core.MerchantConfig
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.typesafe.config.ConfigFactory
import kotlinx.serialization.Serializable
import org.slf4j.LoggerFactory
import services.CybersourceService
import java.io.FileNotFoundException
import java.io.IOException
import java.math.BigInteger
import java.net.HttpURLConnection
import java.net.URI
import java.security.KeyFactory
import java.security.interfaces.RSAPublicKey
import java.security.spec.RSAPublicKeySpec
import java.util.*

object CybersourceUtils {
    private val logger = LoggerFactory.getLogger(CybersourceService::class.java)
    private val config = ConfigFactory.load()
    private val mapper = jacksonObjectMapper()

    val PROFILE_ID = config.getString("app.cybersource.profile_id")
    val KEY_ID = config.getString("app.cybersource.key_id")
    val KEY_SECRET = config.getString("app.cybersource.key_secret")
    val HOST_FLEX = config.getString("app.cybersource.flex_environment")
    val MERCHANT_ID = config.getString("app.cybersource.merchant_id")
    val ORGANIZATION_ID = config.getString("app.cybersource.organization_id")
    val RUN_ENVIRONMENT = config.getString("app.cybersource.run_environment")
    val MICRO_FORM_ORIGIN = config.getString("app.cybersource.micro_form_origin")
    val APPLE_PAY_URL = "https://apple-pay-gateway-cert.apple.com/paymentservices/paymentSession"
    val APPLE_PAY_MERCHANT_IDENTIFIER = config.getString("app.apple.apple_pay_merchant_identifier")
    val APPLE_PAY_KEY_PATH = config.getString("app.apple.apple_pay_key_path")
    val APPLE_PAY_CERTIFICATE_PATH = config.getString("app.apple.apple_pay_certificate_path")

    private val apiClient: ApiClient = ApiClient().apply {
        this.merchantConfig = MerchantConfig(getMerchantConfig())
    }

    fun getMerchantConfig(): Properties {
        val properties = Properties()

        properties.setProperty("authenticationType", "http_Signature")
        properties.setProperty("runEnvironment", RUN_ENVIRONMENT)
        properties.setProperty("merchantID", MERCHANT_ID)
        properties.setProperty("merchantKeyId", KEY_ID)
        properties.setProperty("merchantsecretKey", KEY_SECRET)

        properties.setProperty("useMetaKey", "false")
        properties.setProperty("enableClientCert", "false")
        properties.setProperty("logDirectory", "log")
        properties.setProperty("logFilename", "cybs")
        properties.setProperty("logMaximumSize", "5M")

        return properties
    }

    fun generateSignatureMicroForm(): String? {
        return try {
            val apiInstance = MicroformIntegrationApi(apiClient)
            val request = GenerateCaptureContextRequest().apply {
                clientVersion = "v2.0"
                targetOrigins = listOf(MICRO_FORM_ORIGIN)
                allowedCardNetworks = listOf(
                    "VISA",
                    "MAESTRO",
                    "MASTERCARD",
                    "AMEX",
                    "DISCOVER",
                    "DINERSCLUB",
                    "JCB",
                    "CUP",
                    "CARTESBANCAIRES"
                )
            }
            apiInstance.generateCaptureContext(request)
        } catch (e: ApiException) {
            logger.error("[${e.code}] ${e.responseBody}")
            null
        } catch (e: Throwable) {
            logger.error("${e.message}")
            logger.error(e.message, e)
            null
        }
    }

    fun verifyJwtAndGetDecodedBody(jwt: String): FlexJwt? {
        return try {
            val jwtChunks = jwt.split(".")
            val decoder = Base64.getUrlDecoder()

            val header = String(decoder.decode(jwtChunks[0]))
            val body = String(decoder.decode(jwtChunks[1]))

            val bodyJson = mapper.readValue(body, Map::class.java)

            val exp = bodyJson["exp"] as? Number

            if (exp != null) {
                val expirationDate = Date(exp.toLong() * 1000)
                val now = Date()
                val expiredAt = now.after(expirationDate)
                logger.info("JWT  [Current time: $now] [expires at: $expirationDate] [is expired: ${expiredAt}]")
            }

            val publicKeyJWK = getPublicKeyFromHeader(header)

            val modulus = BigInteger(1, decoder.decode(publicKeyJWK.n))
            val exponent = BigInteger(1, decoder.decode(publicKeyJWK.e))
            val rsaPublicKey =
                KeyFactory.getInstance("RSA").generatePublic(RSAPublicKeySpec(modulus, exponent)) as RSAPublicKey

            val algorithm = Algorithm.RSA256(rsaPublicKey, null)
            val verifier = JWT.require(algorithm).build()

            verifier.verify(jwt)

            mapper.readValue(body, FlexJwt::class.java)
        } catch (e: Throwable) {
            logger.error("[${e.message}] ${e.stackTraceToString()}")
            null
        }
    }

    private fun getPublicKeyFromHeader(jwtHeader: String): JWK {
        val mappedJwtHeader = mapper.readValue<CaptureContextResponseHeader>(jwtHeader)

        val urlString = "https://$RUN_ENVIRONMENT/flex/v2/public-keys/${mappedJwtHeader.kid}"

        return try {
            val url = URI.create(urlString).toURL()
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"

            when (val responseCode = connection.responseCode) {
                HttpURLConnection.HTTP_OK -> {
                    val response = connection.inputStream.bufferedReader().use { it.readText() }
                    connection.disconnect()
                    mapper.readValue<JWK>(response)
                }

                HttpURLConnection.HTTP_NOT_FOUND -> {
                    connection.disconnect()
                    throw IOException("Public key not found for kid: ${mappedJwtHeader.kid}. The JWT might be expired or invalid.")
                }

                HttpURLConnection.HTTP_UNAUTHORIZED, HttpURLConnection.HTTP_FORBIDDEN -> {
                    val errorResponse = connection.errorStream?.bufferedReader()?.use { it.readText() } ?: ""
                    connection.disconnect()
                    throw IOException("Authentication failed (HTTP $responseCode). You might need to add authentication headers. Error: $errorResponse")
                }

                else -> {
                    val errorResponse = connection.errorStream?.bufferedReader()?.use { it.readText() } ?: ""
                    connection.disconnect()
                    throw IOException("Failed to fetch public key. HTTP $responseCode. Error: $errorResponse")
                }
            }
        } catch (e: FileNotFoundException) {
            throw IOException(
                "Public key endpoint returned 404 for URL: $urlString. The key ID '${mappedJwtHeader.kid}' might be expired or invalid.",
                e
            )
        } catch (e: Exception) {
            throw e
        }
    }

    @Serializable
    @JsonIgnoreProperties(ignoreUnknown = true)
    private data class JWK(val n: String, val e: String)

    @Serializable
    private data class CaptureContextResponseHeader(val kid: String, val alg: String)

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FlexJwt(
        val iss: String,
        val exp: Long,
        val type: String,
        val iat: Long,
        val jti: String,
        val content: FlexJwtContent
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FlexJwtContent(
        val paymentInformation: FlexPaymentInformation
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FlexPaymentInformation(
        val card: FlexCard
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FlexCard(
        val expirationYear: FlexExpirationYear,
        val number: FlexCardNumber,
        val securityCode: Map<String, Any>? = emptyMap(), // Empty object in JSON
        val expirationMonth: FlexExpirationMonth
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FlexExpirationYear(
        val value: String
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FlexCardNumber(
        val detectedCardTypes: List<String>,
        val maskedValue: String,
        val bin: String
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FlexExpirationMonth(
        val value: String
    )
}