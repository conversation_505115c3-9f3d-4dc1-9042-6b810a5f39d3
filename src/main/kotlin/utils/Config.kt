package utils

import kotlinx.serialization.json.Json
import java.math.BigDecimal

object Config {
    val currencies = listOf("USD")
    const val DEFAULT_OFFSET = 0
    const val DEFAULT_LIMIT = 1024

    val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
        prettyPrint = true
        encodeDefaults = true
        classDiscriminator = "#class"
    }

    fun generateId(input: String): String {
        return input.replace("[^a-zA-Z0-9& ]", "").replace(" ", "-").lowercase()
    }

    fun isAmountValid(amount: String): Boolean {
        return try {
            BigDecimal(amount)
            true
        } catch (e: Exception) {
            false
        }
    }
}