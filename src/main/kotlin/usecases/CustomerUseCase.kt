package usecases

import models.AppException
import repositories.CustomerRepository
import services.CybersourceService

class CustomerUseCase(
    private val customerRepository: CustomerRepository,
    private val cybersourceService: CybersourceService
) {

    fun getCustomerTokenId(customerId: String, customerEmail: String): String {
        val customer = try {
            customerRepository.findCustomerById(id = customerId)
        } catch (_: AppException.NotFoundException) {
            null
        }
        return if (customer != null) {
            customer.processorId
        } else {
            val customerTokenId =
                cybersourceService.createCustomer(customerId = customerId, customerEmail = customerEmail)
            val dbCustomer = customerRepository.saveCustomer(id = customerId, processorId = customerTokenId)
            dbCustomer.processorId
        }
    }
}