package services

import Api.*
import Invokers.ApiClient
import Invokers.ApiException
import Model.*
import com.cybersource.authsdk.core.MerchantConfig
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.serialization.Serializable
import models.*
import org.slf4j.LoggerFactory
import repositories.PaymentRepository
import utils.CybersourceUtils
import java.math.BigDecimal
import java.util.*

class CybersourceService(val paymentRepository: PaymentRepository) {
    private val logger = LoggerFactory.getLogger(CybersourceService::class.java)
    private val merchantProp: Properties = CybersourceUtils.getMerchantConfig()
    private val profileId = CybersourceUtils.PROFILE_ID
    private val apiClient: ApiClient = ApiClient()
    private val mapper = jacksonObjectMapper()

    private val paymentSessions = mutableMapOf<String, Response.PaymentSession>()
    private val cybersourceErrorFieldMap: Map<String, String> = mapOf(
        "orderInformation.amountDetails.currency" to "Currency",
        "orderInformation.amountDetails.totalAmount" to "Amount",

        "paymentInformation.card.number" to "Card number",
        "paymentInformation.card.expirationMonth" to "Exp. Month",
        "paymentInformation.card.expirationYear" to "Exp. Year",

        "orderInformation.billTo.firstName" to "First Name",
        "orderInformation.billTo.lastName" to "Last Name",
        "orderInformation.billTo.country" to "Country",
        "orderInformation.billTo.email" to "Email",
        "orderInformation.billTo.locality" to "City",
        "orderInformation.billTo.administrativeArea" to "State",
        "orderInformation.billTo.address1" to "Street address",
        "orderInformation.billTo.postalCode" to "Zip code",
        "orderInformation.billTo.phoneNumber" to "Phone number"
    )
    private val cybersourceApplications: Map<String, String> = mapOf(
        "ics_score" to "Advanced Fraud Screen",
        "ics_ap_auth" to "Alt Pay Authorization",
        "ics_ap_auth_reversal" to "Alt Pay Authorization Reversal",
        "ics_ap_billing_agreement" to "Alt Pay Billing Agreement",
        "ics_ap_cancel" to "Alt Pay Cancel",
        "ics_ap_capture" to "Alt Pay Capture",
        "ics_ap_initiate" to "Alt Pay Initiate",
        "ics_ap_options" to "Alt Pay Options",
        "ics_ap_order" to "Alt Pay Order",
        "ics_ap_refund" to "Alt Pay Refund",
        "ics_ap_sale" to "Alt Pay Sale",
        "ics_ap_sessions" to "Alt Pay Session",
        "ics_ap_check_status" to "Alt Pay Service Status",
        "ics_auto_auth_reversal" to "Automatic Authorization Reversal",
        "ics_bank_transfer" to "Bank Transfer",
        "ics_bank_transfer_real_time" to "Bank Transfer Real Time",
        "ics_bank_transfer_refund" to "Bank Transfer Refund",
        "ics_bin_lookup" to "BIN Lookup Service",
        "ics_boleto_payment" to "Boleto Payment",
        "ics_auth" to "Card Authorization",
        "ics_auth_reversal" to "Card Full Authorization Reversal",
        "ics_bill" to "Card Settlement",
        "ics_credit" to "Card Credit",
        "ics_cm_action" to "Case Management Action",
        "ics_china_payment" to "China Payment",
        "ics_china_refund" to "China Refund",
        "ics_auto_full_auth_reversal" to "Credit Card Auto Full Authorization Reversal",
        "ics_auth_refresh" to "Credit Card System Authorization",
        "ics_credit_auth" to "Credit Card Credit Authorization",
        "ics_risk_update" to "Customer List Modification",
        "ics_dcc" to "DCC Lookup",
        "ics_dcc_update" to "DCC Update",
        "ics_decision" to "Decision Manager",
        "ics_dm_event" to "Decision Manager Events",
        "ics_direct_debit" to "Direct Debit",
        "ics_direct_debit_mandate" to "Direct Debit Mandate",
        "ics_direct_debit_refund" to "Direct Debit Refund",
        "ics_direct_debit_validate" to "Direct Debit Validation",
        "ics_ecp_authenticate" to "Electronic Check Authenticate",
        "ics_ecp_credit" to "Electronic Check Credit",
        "ics_ecp_debit" to "Electronic Check Debit",
        "ics_ecp_avs" to "Electronic Check Account Validation",
        "ics_get_masterpass_data" to "Get MasterPass Data",
        "ics_get_visa_checkout_data" to "Get Visa Click to Pay",
        "ics_create_isv" to "Gift Certificate Creation",
        "ics_get_isv_history" to "Gift Certificate History",
        "ics_add_value_to_isv" to "Gift Certificate Increase",
        "ics_get_isv_info" to "Gift Certificate Information",
        "ics_modify_isv" to "Gift Certificate Modification",
        "ics_get_isv_profiles" to "Gift Certificate Profiles",
        "ics_redeem_isv" to "Gift Certificate Redemption",
        "ics_gift_card_activation" to "Gift Card Activation Service",
        "ics_gift_card_balance_inquiry" to "Gift Card Balance Inquiry Service",
        "ics_gift_card_redemption" to "Gift Card Redemption Service",
        "ics_gift_card_refund" to "Gift Card Refund Service",
        "ics_gift_card_reload" to "Gift Card Reload Service",
        "ics_gift_card_reversal" to "Gift Card Reversal Service",
        "ics_gift_card_void" to "Gift Card Void Service",
        "ics_gift_card_timeout_reversal" to "Gift Card Timeout Reversal Service",
        "ics_ifs_setup" to "IFS Setup",
        "ics_ifs_update" to "IFS Update",
        "ics_incremental_auth" to "Incremental Authorization",
        "ics_ipgeo" to "IP Geolocation",
        "ics_oct" to "Original Credit Transaction",
        "ics_pa_setup" to "Payer Authentication Setup",
        "ics_pa_enroll" to "Payer Authentication Enrollment",
        "ics_pa_validate" to "Payer Authentication Validation",
        "ics_authentication_exemptions" to "Visa Exemption Service",
        "paypal_mip_agreement_ipn" to "PayPal Billing Agreement",
        "ics_paypal_button_create" to "PayPal Button Create",
        "ics_paypal_credit" to "PayPal Credit",
        "ics_paypal_authorization" to "PayPal Express Checkout Authorization",
        "ics_paypal_create_agreement" to "PayPal Express Checkout Billing Agreement Create",
        "ics_paypal_update_agreement" to "PayPal Express Checkout Billing Agreement Update",
        "ics_paypal_ec_order_setup" to "PayPal Express Checkout Order Setup",
        "ics_paypal_auth_reversal" to "PayPal Express Checkout Auth Reversal",
        "ics_paypal_ec_do_payment" to "PayPal Express Checkout Do Payment",
        "ics_paypal_do_ref_transaction" to "PayPal Express Checkout Do Reference",
        "ics_paypal_refund" to "PayPal Express Checkout Refund",
        "ics_paypal_do_capture" to "PayPal Express Checkout Settlement",
        "paypal_ipn" to "PayPal Payment",
        "ics_paypal_preapproved_payment" to "PayPal Preapproved Payment",
        "ics_pin_debit_credit" to "PIN Debit Credit",
        "ics_pin_debit_purchase" to "PIN Debit Purchase",
        "ics_pin_debit_reversal" to "PIN Debit Reversal",
        "ics_timeout_pin_debit_reversal" to "PIN Debit Timeout Reversal",
        "ics_pinless_debit" to "PINless Debit",
        "ics_pinless_debit_validate" to "PINless Debit Validation",
        "ics_pinless_debit_reversal" to "PINless Debit Reversal",
        "ics_export" to "Product Export Verification",
        "ics_service_fee_auth" to "Service Fee Authorization",
        "ics_service_fee_auth_reversal" to "Service Fee Authorization Reversal",
        "ics_service_fee_bill" to "Service Fee Settlement",
        "ics_service_fee_credit" to "Service Fee Credit Card Credit",
        "ics_service_fee_ecp_credit" to "Service Fee eCheck Credit",
        "ics_service_fee_ecp_debit" to "Service Fee eCheck Debit",
        "ics_pay_subscription_create" to "Subscription Creation",
        "ics_pay_subscription_create_dup" to "Subscription Creation Duplicates",
        "ics_pay_subscription_delete" to "Subscription Delete",
        "ics_pay_subscription_update" to "Subscription Modification",
        "ics_dav" to "Shipping Address Verification",
        "ics_download" to "Software Download URL",
        "ics_tax" to "Tax Calculation",
        "ics_timeout_auth_reversal" to "Timeout Auth Reversal",
        "ics_timeout_oct_reversal" to "Timeout OCT Reversal",
        "ics_void" to "Voided Transactions",
        "ics_auto_void_auth_reversal" to "Authorization Reversal after Void",
        "ics_pay_subscription_retrieve" to "Subscription Retrieval",
        "ics_ap_create_mandate" to "Alt Pay Direct Debit - Create Mandate",
        "ics_ap_update_mandate" to "Alt Pay Direct Debit - Update Mandate",
        "ics_ap_import_mandate" to "Alt Pay Direct Debit - Import Mandate",
        "ics_ap_revoke_mandate" to "Alt Pay Direct Debit - Revoke Mandate",
        "ics_ap_mandate_status" to "Alt Pay Direct Debit - Mandate status"
    )
    private val cybersourceCvvCodes: Map<String, String> = mapOf(
        "D" to "Transaction determined suspicious by issuing bank",
        "I" to "Card verification number failed processor's data validation check",
        "M" to "Card verification number matched",
        "N" to "Card verification number not matched",
        "P" to "Card verification number not processed by processor for unspecified reason",
        "S" to "Card verification number is on the card but was not included in the request",
        "U" to "Card verification is not supported by the issuing bank",
        "X" to "Card verification is not supported by the card association",
        "1" to "Card verification is not supported for this processor or card type",
        "2" to "Unrecognized result code returned by processor for card verification response",
        "3" to "No result code returned by processor"
    )

    init {
        val merchantConfig = MerchantConfig(merchantProp)
        apiClient.merchantConfig = merchantConfig
    }

    fun createCustomer(customerId: String, customerEmail: String): String {
        val transactionId = UUID.randomUUID().toString()
        return try {
            val apiInstance = CustomerApi(apiClient)

            val requestObj = PostCustomerRequest().apply {
                buyerInformation = Tmsv2customersBuyerInformation().apply {
                    merchantCustomerID = customerId
                    email = customerEmail
                }

                clientReferenceInformation = Tmsv2customersClientReferenceInformation().apply {
                    code = transactionId
                }

                merchantDefinedInformation = mutableListOf(
                    Tmsv2customersMerchantDefinedInformation().apply {
                        name = "data1"
                        value = customerId
                    }
                )
            }

            val responseObj = apiInstance.postCustomer(requestObj, profileId)
            val customerTokenId = responseObj.id
            customerTokenId
        } catch (e: ApiException) {
            when (e.code) {
                400 -> throw AppException.InternalServerErrorException("Bad Request")
                403 -> throw AppException.InternalServerErrorException("Forbidden")
                409 -> throw AppException.ConflictException("payment profile already exists for customer")
                424 -> throw AppException.InternalServerErrorException("Failed Dependency: e.g. The profile represented by the profile-id may not exist or the profile-id was entered incorrectly")
                500 -> throw AppException.InternalServerErrorException("Unexpected error")
                else -> throw AppException.InternalServerErrorException("Unknown error")
            }
        }
    }

    fun getCustomer(id: String): Customer {
        return try {
            val apiInstance = CustomerApi(apiClient)
            val responseObj = apiInstance.getCustomer(id, profileId)
            logger.info("$responseObj")
            val customerTokenId = responseObj.id
            val buyerInformation = responseObj.buyerInformation
            val customerEmail = buyerInformation.email
            val customerId = buyerInformation.merchantCustomerID
            val defaultPaymentInstrument = responseObj.embedded?.defaultPaymentInstrument
            val billTo = defaultPaymentInstrument?.billTo
            val billingAddress = if (billTo != null) Address(
                iso2 = billTo.country,
                state = billTo.administrativeArea,
                city = billTo.locality,
                zipCode = billTo.postalCode,
                streetAddress = billTo.address1,
                extendedAddress = billTo.address2 ?: ""
            ) else null
            Customer(
                id = customerId,
                email = customerEmail,
                billingAddress = billingAddress,
                paymentInstruments = getPaymentInstruments(customerTokenId = customerTokenId)
            )
        } catch (e: ApiException) {
            when (e.code) {
                400 -> throw AppException.InternalServerErrorException("Bad Request")
                403 -> throw AppException.InternalServerErrorException("Forbidden")
                404 -> throw AppException.NotFoundException("Customer payment profile not found")
                410 -> throw AppException.NotFoundException("Customer payment profile not found or deleted")
                424 -> throw AppException.InternalServerErrorException("Failed Dependency: e.g. The profile represented by the profile-id may not exist or the profile-id was entered incorrectly")
                500 -> throw AppException.InternalServerErrorException("Unexpected error")
                else -> throw AppException.InternalServerErrorException("Unknown error")
            }
        }
    }

    fun deleteCustomer(id: String) {
        return try {
            val apiInstance = CustomerApi(apiClient)
            apiInstance.deleteCustomer(id, profileId)
        } catch (e: ApiException) {
            logger.error("ApiException [${e.code}] [${e.responseBody}]")
            when (e.code) {
                400 -> throw AppException.InternalServerErrorException("Bad Request")
                403 -> throw AppException.InternalServerErrorException("Forbidden")
                404 -> throw AppException.NotFoundException("Customer payment profile not found")
                410 -> throw AppException.NotFoundException("Customer payment profile not found or deleted")
                424 -> throw AppException.InternalServerErrorException("Failed Dependency: e.g. The profile represented by the profile-id may not exist or the profile-id was entered incorrectly")
                500 -> throw AppException.InternalServerErrorException("Unexpected error")
                else -> throw AppException.InternalServerErrorException("Unknown error")
            }
        }
    }

    fun updateDefaultPaymentInstrument(
        customerTokenId: String,
        paymentInstrumentId: String,
        isDefault: Boolean
    ): List<PaymentInstrument> {
        return try {
            val customerApi = CustomerApi(apiClient)
            val requestObj = PatchCustomerRequest()

            requestObj.defaultPaymentInstrument = Tmsv2customersDefaultPaymentInstrument().apply {
                id = paymentInstrumentId
            }

            val response = customerApi.patchCustomer(customerTokenId, requestObj, CybersourceUtils.PROFILE_ID, null)

            logger.info("response $response")
            getPaymentInstruments(customerTokenId = customerTokenId)
        } catch (e: ApiException) {
            when (e.code) {
                400 -> throw AppException.NotFoundException("Card id incorrect of invalid")
                403 -> throw AppException.InternalServerErrorException("Forbidden")
                404 -> throw AppException.NotFoundException("Customer payment profile not found")
                410 -> throw AppException.NotFoundException("Customer payment profile not found or deleted")
                412 -> throw AppException.InternalServerErrorException("Precondition Failed: The If-Match request header value does not match the current resources ETag")
                424 -> throw AppException.InternalServerErrorException("Failed Dependency: e.g. The profile represented by the profile-id may not exist or the profile-id was entered incorrectly")
                500 -> throw AppException.InternalServerErrorException("Unexpected error")
                else -> throw AppException.InternalServerErrorException("Unknown error")
            }
        }
    }

    fun updatePaymentInstrument(
        customerTokenId: String,
        paymentInstrumentId: String,
        billingAddress: BillingAddress
    ): List<PaymentInstrument> {
        try {
            val apiInstance = PaymentInstrumentApi(apiClient)
            val requestObj = PatchPaymentInstrumentRequest()



            requestObj.billTo = Tmsv2customersEmbeddedDefaultPaymentInstrumentBillTo().apply {
                this.firstName = billingAddress.firstName
                this.lastName = billingAddress.lastName.ifEmpty { "N/A" }
                this.country = billingAddress.iso2
                this.locality = billingAddress.city.ifEmpty { "N/A" }
                this.administrativeArea = billingAddress.state
                this.address1 = billingAddress.streetAddress.ifEmpty { "N/A" }
                this.address2 = billingAddress.extendedAddress
                this.postalCode = billingAddress.zipCode
            }
            /*  requestObj.instrumentIdentifier = Tmsv2customersEmbeddedDefaultPaymentInstrumentInstrumentIdentifier().apply {
                  this.id = instrumentIdentifierId
              }*/
            val responseObj = apiInstance.patchPaymentInstrument(
                paymentInstrumentId,
                requestObj,
                CybersourceUtils.PROFILE_ID,
                false,
                null
            )
            logger.info("responseObj ${responseObj.billTo}")
            return getPaymentInstruments(customerTokenId = customerTokenId)
        } catch (e: ApiException) {
            when (e.code) {
                400 -> throw AppException.NotFoundException("Card id incorrect of invalid")
                403 -> throw AppException.InternalServerErrorException("Forbidden")
                404 -> throw AppException.NotFoundException("Customer payment profile not found")
                410 -> throw AppException.NotFoundException("Customer payment profile not found or deleted")
                412 -> throw AppException.InternalServerErrorException("Precondition Failed: The If-Match request header value does not match the current resources ETag")
                424 -> throw AppException.InternalServerErrorException("Failed Dependency: e.g. The profile represented by the profile-id may not exist or the profile-id was entered incorrectly")
                500 -> throw AppException.InternalServerErrorException("Unexpected error")
                else -> throw AppException.InternalServerErrorException("Unknown error")
            }
        }
    }

    fun deletePaymentInstrument(customerTokenId: String, paymentInstrumentId: String): List<PaymentInstrument> {
        return try {
            val customerPaymentInstrumentApi = CustomerPaymentInstrumentApi(apiClient)
            val response = customerPaymentInstrumentApi.deleteCustomerPaymentInstrument(
                customerTokenId,
                paymentInstrumentId,
                CybersourceUtils.PROFILE_ID
            )
            logger.info("response $response")
            getPaymentInstruments(customerTokenId = customerTokenId)
        } catch (e: ApiException) {
            logger.error("ApiException [${e.code}] [${e.responseBody}]")
            when (e.code) {
                400 -> throw AppException.NotFoundException("Card id incorrect of invalid")
                403 -> throw AppException.InternalServerErrorException("Forbidden")
                404 -> throw AppException.NotFoundException("Customer payment profile not found")
                409 -> throw AppException.ConflictException("Cannot delete default payment instrument")
                410 -> throw AppException.NotFoundException("Customer payment profile not found or deleted")
                424 -> throw AppException.InternalServerErrorException("Failed Dependency: e.g. The profile represented by the profile-id may not exist or the profile-id was entered incorrectly")
                500 -> throw AppException.InternalServerErrorException("Unexpected error")
                else -> throw AppException.InternalServerErrorException("Unknown error")
            }
        }
    }


    fun generateSession(deviceType: String, paymentMethod: PaymentMethod): Response.PaymentSession {
        try {
            val paymentId = UUID.randomUUID().toString()
            val paymentSignature = CybersourceUtils.generateSignatureMicroForm() ?: throw AppException.InternalServerErrorException("Unable to generate signature")

            val paymentSession = Response.PaymentSession(paymentId = paymentId, paymentSignature = paymentSignature, deviceType = deviceType, paymentMethod = paymentMethod.name, merchantId = CybersourceUtils.MERCHANT_ID, organizationId = CybersourceUtils.ORGANIZATION_ID)
            paymentSessions[paymentId] = paymentSession
            return paymentSession
        } catch (e: Throwable) {
            throw AppException.InternalServerErrorException(e.message ?: "")
        }
    }

    fun getPaymentSession(paymentId: String): Response.PaymentSession {
        return paymentSessions[paymentId] ?: throw AppException.NotFoundException("Payment session not found")
    }

    fun authorizeWithCreditCard(
        paymentId: String,
        paymentData: String? = null,
        paymentInstrumentId: String? = null,
        customerTokenId: String,
        customerId: String,
        customerEmail: String,
        customerPhone: String?,
        amount: BigDecimal,
        currency: String,
        storeInVault: Boolean,
        billingAddress: BillingAddress? = null,
        deviceFingerprint: DeviceFingerprint? = null
    ): Response.Authorized {
        val transactionId = UUID.randomUUID().toString()
        val paymentSession = getPaymentSession(paymentId = paymentId)
        paymentRepository.addPayment(paymentId = paymentId, customerId = customerId, customerTokenId = customerTokenId, amount = amount, currency = currency, paymentMethod = PaymentMethod.valueOf(paymentSession.paymentMethod))

        try {
            val requestObj = CreatePaymentRequest()

            var cardCanonicalName = ""
            var cardPrefix = ""
            var cardSuffix = ""
            var cardType = ""
            var cardExpirationYear = ""
            var cardExpirationMonth = ""

            requestObj.clientReferenceInformation = Ptsv2paymentsClientReferenceInformation().apply {
                code = transactionId
            }

            requestObj.orderInformation = Ptsv2paymentsOrderInformation().apply {
                this.amountDetails = Ptsv2paymentsOrderInformationAmountDetails().apply {
                    this.totalAmount = amount.toPlainString()
                    this.currency = currency
                }

                billingAddress?.let {
                    this.billTo = Ptsv2paymentsOrderInformationBillTo().apply {
                        this.firstName = billingAddress.firstName
                        this.lastName = billingAddress.lastName.ifEmpty { "N/A" }
                        this.country = billingAddress.iso2
                        this.locality = billingAddress.city.ifEmpty { "N/A" }
                        this.administrativeArea = billingAddress.state
                        this.address1 = billingAddress.streetAddress.ifEmpty { "N/A" }
                        this.address2 = billingAddress.extendedAddress
                        this.postalCode = billingAddress.zipCode.ifEmpty { "N/A" }

                        this.email = customerEmail
                        customerPhone?.let { this.phoneNumber = customerPhone }
                    }

                }
            }

            requestObj.processingInformation = Ptsv2paymentsProcessingInformation().apply {
                this.setCapture(false)
                if (storeInVault) {
                    actionList = listOf("TOKEN_CREATE")
                    actionTokenTypes = listOf("paymentInstrument", "instrumentIdentifier")
                }
            }

            requestObj.paymentInformation = Ptsv2paymentsPaymentInformation().apply {
                this.customer = Ptsv2paymentsPaymentInformationCustomer().apply {
                    this.id = customerTokenId
                }
                /*    this.card = Ptsv2paymentsPaymentInformationCard().apply { this
                          this.number("****************")
                          this.expirationMonth("12")
                          this.expirationYear("2020")
                      }*/
            }

            paymentData?.let {
                val flexJwt = CybersourceUtils.verifyJwtAndGetDecodedBody(paymentData)
                    ?: throw AppException.BadRequestException("Invalid payment, expired to invalid")

                logger.info("flexJwt $flexJwt")
                cardCanonicalName = "${flexJwt.content.paymentInformation.card.number.bin}${
                    flexJwt.content.paymentInformation.card.number.maskedValue.substring(flexJwt.content.paymentInformation.card.number.bin.length)
                }"
                cardExpirationYear = flexJwt.content.paymentInformation.card.expirationYear.value
                cardExpirationMonth = flexJwt.content.paymentInformation.card.expirationMonth.value
                cardPrefix = flexJwt.content.paymentInformation.card.number.bin
                cardSuffix = cardCanonicalName.takeLast(4)
                cardType = getCardName(flexJwt.content.paymentInformation.card.number.detectedCardTypes.first())
            }

            paymentInstrumentId?.let {
                val paymentInstrument =
                    getPaymentInstruments(customerTokenId = customerTokenId).find { it.id == paymentInstrumentId }
                        ?: throw AppException.NotFoundException("Payment instrument $paymentInstrumentId not found")
                cardCanonicalName = paymentInstrument.canonicalName
                cardPrefix = paymentInstrument.canonicalName.take(6)
                cardType = paymentInstrument.brand
                cardSuffix = paymentInstrument.last4
            }

            deviceFingerprint?.let {
                requestObj.deviceInformation = Ptsv2paymentsDeviceInformation().apply {
                    this.deviceType = deviceType
                    this.fingerprintSessionId = deviceFingerprint.sessionId
                    this.ipAddress = deviceFingerprint.ipAddress
                    this.hostName = deviceFingerprint.hostname
                    this.userAgent = deviceFingerprint.userAgent
                }
            }

            paymentData?.let {
                requestObj.tokenInformation = Ptsv2paymentsTokenInformation().apply {
                    this.transientTokenJwt = paymentData
                }
            }

            paymentInstrumentId?.let {
                requestObj.paymentInformation.paymentInstrument =
                    Ptsv2paymentsPaymentInformationPaymentInstrument().apply {
                        this.id = paymentInstrumentId
                    }
            }

            if (paymentSession.paymentMethod == PaymentMethod.DEFAULT_VAULT.name) {
                val paymentInstrument = getPaymentInstruments(customerTokenId = customerTokenId).find { it.isDefault }
                    ?: throw AppException.NotFoundException("Payment instrument $paymentInstrumentId not found")
                cardCanonicalName = paymentInstrument.canonicalName
                cardPrefix = paymentInstrument.canonicalName.take(6)
                cardType = paymentInstrument.brand
                cardSuffix = paymentInstrument.last4
            }

            val responseObj = PaymentsApi(apiClient).createPayment(requestObj)
            logger.info("AUTHORIZATION RESPONSE: [${responseObj}]")

            val authorizationId = responseObj.id
            val submitTimeUtc = responseObj.submitTimeUtc
            val status = responseObj.status

            val responseTokenInformation = responseObj.tokenInformation
            val responsePaymentInformation = responseObj.paymentInformation
            val processorInformation = responseObj.processorInformation
            val responseOrderInformation = responseObj.orderInformation
            val responseRisqueInformation = responseObj.riskInformation
            val errorInformation: PtsV2PaymentsPost201ResponseErrorInformation? = responseObj.errorInformation

            val errorCode = errorInformation?.reason
            val errorMessage = errorInformation?.message
            val errorDetails = errorInformation?.details?.map { "${it.field} : ${it.reason}" }?.joinToString { "," }

            val responsePaymentInstrumentId =
                responsePaymentInformation?.paymentInstrument?.id ?: responseTokenInformation?.paymentInstrument?.id
            val responseInstrumentIdentifierId = responsePaymentInformation?.instrumentIdentifier?.id
                ?: responseTokenInformation?.instrumentIdentifier?.id
            val customerPaymentAccountReferenceNumber = processorInformation?.paymentAccountReferenceNumber ?: ""

            val avsCode = processorInformation?.avs?.code ?: ""
            val cvvCode = processorInformation?.cardVerification?.resultCode ?: ""

            logger.info("status $status")
            logger.info("authorizationId $authorizationId")
            logger.info("submitTimeUtc $submitTimeUtc")
            logger.info("responsePaymentInstrumentId $responsePaymentInstrumentId")
            logger.info("responseInstrumentIdentifierId $responseInstrumentIdentifierId")
            logger.info("customerPaymentAccountReferenceNumber $customerPaymentAccountReferenceNumber")
            logger.info("avsCode $avsCode")
            logger.info("cvvCode $cvvCode")
            logger.info("cardCanonicalName $cardCanonicalName")
            logger.info("cardExpirationMonth $cardExpirationMonth")
            logger.info("cardExpirationYear $cardExpirationYear")
            logger.info("errorCode $errorCode")
            logger.info("errorMessage $errorMessage")

            if (status != "AUTHORIZED") {
                paymentRepository.updatePaymentAuthorization(
                    paymentId = paymentId,
                    paymentStatus = PaymentStatus.FAILED,
                    merchantStatus = status,
                    canonicalName = cardCanonicalName,
                    expMonth = cardExpirationMonth,
                    expYear = cardExpirationYear,
                    cardType = cardType
                )
                paymentRepository.addPaymentEventAuthorization(
                    paymentId = paymentId,
                    transactionId = transactionId,
                    referenceId = authorizationId,
                    paymentEventType = PaymentEventType.AUTHORIZATION,
                    paymentEventStatus = PaymentEventStatus.FAILED,
                    amount = amount,
                    currency = currency,
                    errorCode = errorCode,
                    errorMessage = errorMessage,
                    metadata = errorDetails
                )
            }

            when (status) {
                "AUTHORIZED" -> {
                    val responseAmountDetails = responseOrderInformation.amountDetails
                    val responseAmount = responseAmountDetails.authorizedAmount
                    val responseCurrency = responseAmountDetails.currency

                    val risqueProviders = responseRisqueInformation.providers?.entries
                    val dmData = DecisionManagerData()
                    if (risqueProviders != null) {
                        for ((_, value) in risqueProviders) {
                            value.forEach { (k, v) ->
                                when (k) {
                                    "smart_id" -> dmData.smartId = v
                                    "strong_id" -> dmData.strongId = v
                                    "hash" -> dmData.hash = v
                                    "device_behavior_info" -> dmData.deviceBehaviorInfo = v
                                    "true_ipaddress_country" -> dmData.trueIpaddressCountry = v
                                    "true_ipaddress_city" -> dmData.trueIpaddressCity = v
                                    "true_ipaddress_state" -> dmData.trueIpaddressState = v
                                    "true_ipaddress" -> dmData.trueIpaddress = v
                                    "device_match" -> dmData.deviceMatch = v
                                    "device_match_again" -> dmData.deviceMatchAgain = v
                                    "agent_type" -> dmData.agentType = v
                                    "test_risk_rating" -> dmData.testRiskRating = v
                                    "screen_resolution" -> dmData.screenResolution = v
                                    "first_encounter" -> dmData.firstEncounter = v
                                }
                            }
                        }
                    }

                    val deviceFingerprintId = dmData.smartId

                    logger.info("responsePaymentInstrumentId $responsePaymentInstrumentId")
                    logger.info("responseInstrumentIdentifierId $responseInstrumentIdentifierId")
                    logger.info("customerPaymentAccountReferenceNumber $customerPaymentAccountReferenceNumber")
                    logger.info("deviceFingerprintId $deviceFingerprintId")

                    paymentRepository.updatePaymentAuthorization(
                        paymentId = paymentId,
                        paymentStatus = PaymentStatus.AUTHORIZED,
                        merchantStatus = status,
                        canonicalName = cardCanonicalName,
                        cardType = cardType,
                        cardPrefix = cardPrefix,
                        cardSuffix = cardSuffix,
                        expMonth = cardExpirationMonth,
                        expYear = cardExpirationYear
                    )
                    paymentRepository.addPaymentEventAuthorization(
                        paymentId = paymentId,
                        transactionId = transactionId,
                        referenceId = authorizationId,
                        paymentEventType = PaymentEventType.AUTHORIZATION,
                        paymentEventStatus = PaymentEventStatus.SUCCESS,
                        amount = BigDecimal(responseAmount),
                        currency = responseCurrency
                    )
                    return Response.Authorized(
                        date = submitTimeUtc,
                        paymentId = paymentId,
                        canonicalName = cardCanonicalName,
                        cardType = cardType,
                        amount = responseAmount,
                        currency = responseCurrency
                    )
                }

                "PARTIAL_AUTHORIZED" -> throw AppException.BadRequestException("Payment partially authorized")
                "AUTHORIZED_PENDING_REVIEW" -> throw AppException.BadRequestException("Authorization pending review")
                "AUTHORIZED_RISK_DECLINED" -> throw AppException.BadRequestException("payment declined")
                "PENDING_AUTHENTICATION" -> throw AppException.BadRequestException("Pending review")
                "PENDING_REVIEW" -> throw AppException.BadRequestException("Pending review")
                "DECLINED" -> throw AppException.BadRequestException("Payment declined.")
                "INVALID_REQUEST" -> throw AppException.BadRequestException("Payment Request invalid")
                else -> throw AppException.InternalServerErrorException("Unable to process payment")
            }
        } catch (e: ApiException) {
            val cyberSourceError = mapper.readValue<CybersourceError>(e.responseBody)
            val referenceId = cyberSourceError.id
            val errorCode = cyberSourceError.reason
            val errorMessage = cyberSourceError.message
            val errorDetails =
                cyberSourceError.details?.joinToString(", ") { cyberSourceErrorDetail -> "${cybersourceErrorFieldMap[cyberSourceErrorDetail.field]} missing" }
                    ?: "Card or billing data missing"

            logger.error("referenceId: $referenceId")
            logger.error("errorCode: $errorCode")
            logger.error("errorMessage: $errorMessage")
            logger.error("errorDetails: $errorDetails")

            paymentRepository.updatePaymentAuthorization(
                paymentId = paymentId,
                paymentStatus = PaymentStatus.FAILED,
                merchantStatus = cyberSourceError.status
            )
            paymentRepository.addPaymentEventAuthorization(
                paymentId = paymentId,
                transactionId = transactionId,
                referenceId = referenceId,
                paymentEventType = PaymentEventType.AUTHORIZATION,
                paymentEventStatus = PaymentEventStatus.FAILED,
                amount = amount,
                currency = currency,
                errorCode = errorCode,
                errorMessage = errorMessage,
                metadata = errorDetails
            )

            when (e.code) {
                400 -> when (errorCode) {
                    "MISSING_FIELD" -> throw AppException.BadRequestException(errorDetails)
                    "INVALID_DATA" -> throw AppException.BadRequestException(errorDetails)
                    "DUPLICATE_REQUEST" -> throw AppException.ConflictException("transaction already in progressed")
                    "INVALID_CARD" -> throw AppException.BadRequestException("Card is invalid")
                    "CARD_TYPE_NOT_ACCEPTED" -> throw AppException.BadRequestException("Card type not accepted")
                    "INVALID_MERCHANT_CONFIGURATION" -> throw AppException.InternalServerErrorException("Invalid merchant configuration")
                    "PROCESSOR_UNAVAILABLE" -> throw AppException.InternalServerErrorException("Processor unavailable")
                    "INVALID_AMOUNT" -> throw AppException.BadRequestException("Invalid amount")
                    "INVALID_CARD_TYPE" -> throw AppException.BadRequestException("Invalid card type")
                    "INVALID_PAYMENT_ID" -> throw AppException.BadRequestException("Invalid payment reference")
                    else -> throw AppException.BadRequestException("Unable to process payment")
                }

                500 -> when (errorCode) {
                    "SYSTEM_ERROR" -> throw AppException.InternalServerErrorException("SYSTEM_ERROR")
                    "SERVER_TIMEOUT" -> throw AppException.InternalServerErrorException("SERVER_TIMEOUT")
                    "SERVICE_TIMEOUT" -> throw AppException.InternalServerErrorException("SERVICE_TIMEOUT")
                    else -> throw AppException.InternalServerErrorException("UNKNOWN ERROR")
                }

                else -> {
                    throw AppException.InternalServerErrorException("UNKNOWN ERROR")
                }
            }
        }
    }

    fun captureAuthorization(paymentId: String): Response.Captured {
        val transactionId = UUID.randomUUID().toString()
        val payment = paymentRepository.findPaymentById(id = paymentId)
        if (payment.paymentStatus != PaymentStatus.AUTHORIZED) {
            throw AppException.ConflictException("Transaction already processed or not in correct state")
        }
        val paymentEvent = paymentRepository.findPaymentEventsByPaymentId(paymentId = paymentId)
            .firstOrNull { it.paymentEventType == PaymentEventType.AUTHORIZATION && it.paymentEventStatus == PaymentEventStatus.SUCCESS }
            ?: throw AppException.BadRequestException("No authorization events found for payment $paymentId")
        return try {
            val apiInstance = CaptureApi(apiClient)
            val requestObj = CapturePaymentRequest()

            val clientReferenceInformation = Ptsv2paymentsClientReferenceInformation().apply {
                code = transactionId
            }
            val orderInformation = Ptsv2paymentsidcapturesOrderInformation().apply {
                amountDetails = Ptsv2paymentsidcapturesOrderInformationAmountDetails().apply {
                    this.totalAmount = payment.amount
                    this.currency = currency
                }
            }

            requestObj.clientReferenceInformation = clientReferenceInformation
            requestObj.orderInformation = orderInformation

            val responseObj = apiInstance.capturePayment(requestObj, paymentEvent.referenceId)
            logger.info("CAPTURE RESPONSE: [$responseObj]")
            val captureId = responseObj.id
            val status = responseObj.status // "PENDING"
            val submitTimeUtc = responseObj.submitTimeUtc

            val responseOrderInformationResponse = responseObj.orderInformation
            val responseAmountDetails = responseOrderInformationResponse.amountDetails
            val responseTotalAmount = responseAmountDetails.totalAmount
            val responseCurrency = responseAmountDetails.currency

            paymentRepository.updatePayment(
                paymentId = paymentId,
                paymentStatus = PaymentStatus.CAPTURED,
                merchantStatus = status
            )
            paymentRepository.addPaymentEvent(
                transactionId = transactionId,
                paymentId = paymentId,
                referenceId = captureId,
                paymentEventType = PaymentEventType.CAPTURE,
                paymentEventStatus = PaymentEventStatus.SUCCESS,
                amount = BigDecimal(responseTotalAmount),
                currency = responseCurrency
            )

            Response.Captured(
                date = submitTimeUtc,
                paymentId = paymentId,
                amount = responseTotalAmount,
                currency = responseCurrency,
                canonicalName = payment.canonicalName!!,
                cardType = payment.cardType!!
            )
        } catch (e: ApiException) {
            val cyberSourceError = mapper.readValue<CybersourceError>(e.responseBody)
            val referenceId = cyberSourceError.id
            val errorCode = cyberSourceError.reason
            val errorMessage = cyberSourceError.message
            val errorDetails =
                cyberSourceError.details?.joinToString(", ") { cyberSourceErrorDetail -> "${cybersourceErrorFieldMap[cyberSourceErrorDetail.field]} missing" }
                    ?: "Card or billing data missing"

            logger.error("referenceId: $referenceId")
            logger.error("errorCode: $errorCode")
            logger.error("errorMessage: $errorMessage")
            logger.error("errorDetails: $errorDetails")

            paymentRepository.updatePaymentAuthorization(
                paymentId = paymentId,
                paymentStatus = PaymentStatus.FAILED,
                merchantStatus = cyberSourceError.status
            )
            paymentRepository.addPaymentEventAuthorization(
                paymentId = paymentId,
                transactionId = transactionId,
                referenceId = referenceId,
                paymentEventType = PaymentEventType.CAPTURE,
                paymentEventStatus = PaymentEventStatus.FAILED,
                amount = BigDecimal(payment.amount),
                currency = payment.currency,
                errorCode = errorCode,
                errorMessage = errorMessage,
                metadata = errorDetails
            )

            when (e.code) {
                400 -> when (errorCode) {
                    "MISSING_FIELD" -> throw AppException.BadRequestException(errorDetails)
                    "INVALID_DATA" -> throw AppException.BadRequestException(errorDetails)
                    "DUPLICATE_REQUEST" -> throw AppException.ConflictException("transaction already in progressed")
                    "INVALID_MERCHANT_CONFIGURATION" -> throw AppException.InternalServerErrorException("Invalid merchant configuration")
                    "EXCEEDS_AUTH_AMOUNT" -> throw AppException.BadRequestException("Amount exceeds the originally authorized amount")
                    "AUTH_ALREADY_REVERSED" -> throw AppException.BadRequestException("The authorization has already been reversed")
                    "TRANSACTION_ALREADY_SETTLED" -> throw AppException.BadRequestException("The transaction has already been settled")
                    "INVALID_AMOUNT" -> throw AppException.BadRequestException("Invalid amount")
                    "MISSING_AUTH" -> throw AppException.InternalServerErrorException("Missing auth")
                    "TRANSACTION_ALREADY_REVERSED_OR_SETTLED" -> throw AppException.BadRequestException("The transaction has already been reversed or settled")
                    "NOT_SUPPORTED" -> throw AppException.InternalServerErrorException("Not supported")
                    else -> throw AppException.BadRequestException("Unable to capture payment")
                }

                500 -> when (errorCode) {
                    "SYSTEM_ERROR" -> throw AppException.InternalServerErrorException("SYSTEM_ERROR")
                    "SERVER_TIMEOUT" -> throw AppException.InternalServerErrorException("SERVER_TIMEOUT")
                    "SERVICE_TIMEOUT" -> throw AppException.InternalServerErrorException("SERVICE_TIMEOUT")
                    else -> throw AppException.InternalServerErrorException("UNKNOWN ERROR")
                }

                else -> {
                    throw AppException.InternalServerErrorException("UNKNOWN ERROR")
                }
            }
        }
    }

    fun reverseAuthorization(paymentId: String, reason: String): Response.Reversed {
        val transactionId = UUID.randomUUID().toString()
        val payment = paymentRepository.findPaymentById(id = paymentId)
        if (payment.paymentStatus != PaymentStatus.AUTHORIZED) {
            throw AppException.ConflictException("Transaction already processed or not in correct state")
        }
        val paymentEvent = paymentRepository.findPaymentEventsByPaymentId(paymentId = paymentId)
            .firstOrNull { it.paymentEventType == PaymentEventType.AUTHORIZATION && it.paymentEventStatus == PaymentEventStatus.SUCCESS }
            ?: throw AppException.BadRequestException("No authorization events found for payment $paymentId")
        return try {
            val apiInstance = ReversalApi(apiClient)
            val requestObj = AuthReversalRequest()

            val clientReferenceInformation = Ptsv2paymentsidreversalsClientReferenceInformation().apply {
                code = transactionId
            }
            val reversalInformation = Ptsv2paymentsidreversalsReversalInformation().apply {
                this.reason = reason
                this.amountDetails = Ptsv2paymentsidreversalsReversalInformationAmountDetails().apply {
                    this.totalAmount = payment.amount
                    this.currency = currency
                }
            }

            requestObj.clientReferenceInformation = clientReferenceInformation
            requestObj.reversalInformation = reversalInformation

            val responseObj = apiInstance.authReversal(paymentEvent.referenceId, requestObj)
            logger.info("REVERSAL RESPONSE: [$responseObj]")
            val reversalId = responseObj.id
            val status = responseObj.status // REVERSED
            val submitTimeUtc = responseObj.submitTimeUtc
            val responseAmountDetail = responseObj.reversalAmountDetails
            val reversedCurrency = responseAmountDetail.currency
            val reversedAmount = responseAmountDetail.reversedAmount

            paymentRepository.updatePayment(
                paymentId = paymentId,
                paymentStatus = PaymentStatus.REVERSED,
                merchantStatus = status
            )
            paymentRepository.addPaymentEvent(
                transactionId = transactionId,
                paymentId = paymentId,
                referenceId = reversalId,
                amount = BigDecimal(reversedAmount),
                currency = reversedCurrency,
                paymentEventType = PaymentEventType.REVERSAL,
                paymentEventStatus = PaymentEventStatus.SUCCESS
            )

            Response.Reversed(
                paymentId = paymentId,
                date = submitTimeUtc,
                amount = reversedAmount,
                currency = reversedCurrency
            )
        } catch (e: ApiException) {
            val cyberSourceError = mapper.readValue<CybersourceError>(e.responseBody)
            val referenceId = cyberSourceError.id
            val errorCode = cyberSourceError.reason
            val errorMessage = cyberSourceError.message
            val errorDetails =
                cyberSourceError.details?.joinToString(", ") { cyberSourceErrorDetail -> "${cybersourceErrorFieldMap[cyberSourceErrorDetail.field]} missing" }
                    ?: "Card or billing data missing"

            logger.error("referenceId: $referenceId")
            logger.error("errorCode: $errorCode")
            logger.error("errorMessage: $errorMessage")
            logger.error("errorDetails: $errorDetails")

            paymentRepository.updatePaymentAuthorization(
                paymentId = paymentId,
                paymentStatus = PaymentStatus.FAILED,
                merchantStatus = cyberSourceError.status
            )
            paymentRepository.addPaymentEventAuthorization(
                paymentId = paymentId,
                transactionId = transactionId,
                referenceId = referenceId,
                paymentEventType = PaymentEventType.REVERSAL,
                paymentEventStatus = PaymentEventStatus.FAILED,
                amount = BigDecimal(payment.amount),
                currency = payment.currency,
                errorCode = errorCode,
                errorMessage = errorMessage,
                metadata = errorDetails
            )
            when (e.code) {
                400 -> when (errorCode) {
                    "MISSING_FIELD" -> throw AppException.BadRequestException(errorDetails)
                    "INVALID_DATA" -> throw AppException.BadRequestException(errorDetails)
                    "DUPLICATE_REQUEST" -> throw AppException.ConflictException("transaction already in progressed")
                    "INVALID_MERCHANT_CONFIGURATION" -> throw AppException.InternalServerErrorException("Invalid merchant configuration")
                    "PROCESSOR_UNAVAILABLE" -> throw AppException.InternalServerErrorException("Processor unavailable")
                    "AUTH_ALREADY_REVERSED" -> throw AppException.BadRequestException("The authorization has already been reversed")
                    "TRANSACTION_ALREADY_SETTLED" -> throw AppException.BadRequestException("The transaction has already been settled")
                    "INVALID_AMOUNT" -> throw AppException.BadRequestException("Invalid amount")
                    "MISSING_AUTH" -> throw AppException.InternalServerErrorException("Missing auth")
                    "TRANSACTION_ALREADY_REVERSED_OR_SETTLED" -> throw AppException.BadRequestException("The transaction has already been reversed or settled")
                    "NOT_SUPPORTED" -> throw AppException.InternalServerErrorException("Not supported")
                    else -> throw AppException.BadRequestException("Unable to reverse payment")
                }

                500 -> when (errorCode) {
                    "SYSTEM_ERROR" -> throw AppException.InternalServerErrorException("SYSTEM_ERROR")
                    "SERVER_TIMEOUT" -> throw AppException.InternalServerErrorException("SERVER_TIMEOUT")
                    "SERVICE_TIMEOUT" -> throw AppException.InternalServerErrorException("SERVICE_TIMEOUT")
                    else -> throw AppException.InternalServerErrorException("UNKNOWN ERROR")
                }

                else -> {
                    throw AppException.InternalServerErrorException("UNKNOWN ERROR")
                }

            }
        }
    }

    fun voidCapture(paymentId: String): Response.Voided {
        val transactionId = UUID.randomUUID().toString()
        val payment = paymentRepository.findPaymentById(id = paymentId)
        if (payment.paymentStatus != PaymentStatus.CAPTURED) {
            throw AppException.ConflictException("Transaction already processed or not in correct state")
        }
        val paymentEvent = paymentRepository.findPaymentEventsByPaymentId(paymentId = paymentId)
            .firstOrNull { it.paymentEventType == PaymentEventType.CAPTURE && it.paymentEventStatus == PaymentEventStatus.SUCCESS }
            ?: throw AppException.BadRequestException("No authorization events found for payment $paymentId")
        return try {
            val apiInstance = VoidApi(apiClient)
            val requestObj = VoidCaptureRequest()

            val clientReferenceInformation = Ptsv2paymentsidreversalsClientReferenceInformation().apply {
                code = transactionId
            }
            requestObj.clientReferenceInformation = clientReferenceInformation

            val responseObj = apiInstance.voidCapture(requestObj, paymentEvent.referenceId)
            val voidId = responseObj.id
            val submitTimeUtc = responseObj.submitTimeUtc
            val status = responseObj.status
            val voidAmountDetails = responseObj.voidAmountDetails
            val voidAmount = voidAmountDetails.voidAmount
            val voidCurrency = voidAmountDetails.currency

            paymentRepository.updatePayment(
                paymentId = paymentId,
                paymentStatus = PaymentStatus.VOIDED,
                merchantStatus = status
            )
            paymentRepository.addPaymentEvent(
                transactionId = transactionId,
                paymentId = paymentId,
                referenceId = voidId,
                amount = BigDecimal(voidAmount),
                currency = voidCurrency,
                paymentEventType = PaymentEventType.VOID,
                paymentEventStatus = PaymentEventStatus.SUCCESS
            )

            Response.Voided(paymentId = paymentId, date = submitTimeUtc, amount = voidAmount, currency = voidCurrency)
        } catch (e: ApiException) {
            val cyberSourceError = mapper.readValue<CybersourceError>(e.responseBody)
            val referenceId = cyberSourceError.id
            val errorCode = cyberSourceError.reason
            val errorMessage = cyberSourceError.message
            val errorDetails =
                cyberSourceError.details?.joinToString(", ") { cyberSourceErrorDetail -> "${cybersourceErrorFieldMap[cyberSourceErrorDetail.field]} missing" }
                    ?: "Card or billing data missing"

            logger.error("referenceId: $referenceId")
            logger.error("errorCode: $errorCode")
            logger.error("errorMessage: $errorMessage")
            logger.error("errorDetails: $errorDetails")

            paymentRepository.updatePaymentAuthorization(
                paymentId = paymentId,
                paymentStatus = PaymentStatus.FAILED,
                merchantStatus = cyberSourceError.status
            )
            paymentRepository.addPaymentEventAuthorization(
                paymentId = paymentId,
                transactionId = transactionId,
                referenceId = referenceId,
                paymentEventType = PaymentEventType.VOID,
                paymentEventStatus = PaymentEventStatus.FAILED,
                amount = BigDecimal(payment.amount),
                currency = payment.currency,
                errorCode = errorCode,
                errorMessage = errorMessage,
                metadata = errorDetails
            )

            throw AppException.BadRequestException("Unable to process payment")
        }
    }

    private fun getCardName(code: String): String = when (code) {
        "001" -> "Visa"
        "002" -> "Mastercard"
        "003" -> "American Express"
        "004" -> "Discover"
        "005" -> "Diners Club"
        "006" -> "Carte Blanche"
        "007" -> "JCB"
        "014" -> "EnRoute"
        "021" -> "JAL"
        "024" -> "Maestro (UK Domestic)"
        "031" -> "Delta"
        "033" -> "Visa Electron"
        "034" -> "Dankort"
        "036" -> "Cartes Bancaires"
        "037" -> "Carta"
        "039" -> "Encoded account number"
        "040" -> "UATP"
        "042" -> "Maestro (International)"
        "050" -> "Hipercard"
        "051" -> "Aura"
        "054" -> "Elo"
        "061" -> "RuPay"
        "062" -> "China UnionPay"
        "058" -> "Carnet"
        "paypal_account" -> "paypal"
        else -> "Unknown"
    }

    private fun getPaymentInstruments(customerTokenId: String): List<PaymentInstrument> {
        return try {
            val apiInstance = CustomerPaymentInstrumentApi(apiClient)
            val responseObj =
                apiInstance.getCustomerPaymentInstrumentsList(customerTokenId, CybersourceUtils.PROFILE_ID, 0L, 42L)
            logger.info("responseObj $responseObj")
            responseObj.embedded?.paymentInstruments?.mapNotNull { paymentInstrument ->
                val paymentInstrumentId = paymentInstrument.id
                val isDefault = paymentInstrument.Default()
                paymentInstrument.state // TODO should be == ACTIVE
                val paymentInstrumentCard = paymentInstrument.card
                val billTo = paymentInstrument.billTo
                val instrumentIdentifier = paymentInstrument.embedded.instrumentIdentifier

                instrumentIdentifier.state //  TODO should be == ACTIVE
                val instrumentIdentifierCard = instrumentIdentifier.card
                val canonicalName = instrumentIdentifierCard.number
                val last4 = canonicalName.substring(canonicalName.length - 4, canonicalName.length)

                val cardBrand = getCardName(paymentInstrumentCard.type)
                paymentInstrumentCard.expirationYear
                paymentInstrumentCard.expirationMonth

                val billingAddress = BillingAddress(
                    firstName = billTo.firstName,
                    lastName = billTo.lastName,
                    iso2 = billTo.country,
                    state = billTo.administrativeArea,
                    city = billTo.locality,
                    zipCode = billTo.postalCode,
                    streetAddress = billTo.address1,
                    extendedAddress = billTo.address2 ?: ""
                )

                logger.info("paymentInstrument ${paymentInstrument.id} -> ${canonicalName} -> ${billingAddress}")

                PaymentInstrument(
                    id = paymentInstrumentId,
                    paymentMethod = PaymentMethod.CREDIT_CARD.name,
                    canonicalName = canonicalName,
                    last4 = last4,
                    isDefault = isDefault,
                    brand = cardBrand
                )
            }?.toList() ?: emptyList()
        } catch (e: ApiException) {
            logger.error("ApiException [${e.code}] [${e.responseBody}]")
            when (e.code) {
                400 -> throw AppException.InternalServerErrorException("Bad Request")
                403 -> throw AppException.InternalServerErrorException("Forbidden")
                404 -> throw AppException.NotFoundException("Customer payment profile not found")
                410 -> throw AppException.NotFoundException("Customer payment profile not found or deleted")
                424 -> throw AppException.InternalServerErrorException("Failed Dependency: e.g. The profile represented by the profile-id may not exist or the profile-id was entered incorrectly")
                500 -> throw AppException.InternalServerErrorException("Unexpected error")
                else -> throw AppException.InternalServerErrorException("Unknown error")
            }
        }
    }

    @Serializable
    private data class DecisionManagerData(
        var smartId: String = "",
        var strongId: String = "",
        var hash: String = "",
        var deviceBehaviorInfo: String = "",
        var trueIpaddressCountry: String = "",
        var trueIpaddressCity: String = "",
        var trueIpaddressState: String = "",
        var trueIpaddress: String = "",
        var deviceMatch: String = "",
        var deviceMatchAgain: String = "",
        var agentType: String = "",
        var testRiskRating: String = "",
        var screenResolution: String = "",
        var firstEncounter: String = ""
    )

    /**
     * Cybersource error response model
     *
     * @param id optional error id
     * @param submitTimeUtc timestamp of submission
     * @param status error status
     * @param reason main error reason
     * @param message human-readable message
     * @param details optional array of field-level error details
     * @param errorInformation optional error information object
     */
    @Serializable
    private data class CybersourceError(
        val id: String?,
        val submitTimeUtc: String,
        val status: String,
        val reason: String,
        val message: String,
        val details: List<CybersourceErrorDetail>?,
        val errorInformation: CybersourceErrorInformation?
    )

    /**
     * Field-level error detail
     *
     * @param field field name that caused the error
     * @param reason reason for the error (MISSING_FIELD | INVALID_DATA, etc.)
     */
    @Serializable
    private data class CybersourceErrorDetail(
        val field: String,
        val reason: String
    )

    /**
     * Additional error information
     *
     * @param reason error reason
     * @param message error message
     */
    @Serializable
    private data class CybersourceErrorInformation(
        val reason: String,
        val message: String
    )
}