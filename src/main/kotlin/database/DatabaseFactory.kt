package database

import com.typesafe.config.Config
import com.typesafe.config.ConfigFactory
import org.jetbrains.exposed.v1.jdbc.Database
import org.jetbrains.exposed.v1.jdbc.transactions.transaction

object DatabaseFactory {
    private val config: Config = ConfigFactory.load()

    private val dbUser: String = config.getString("app.database.user")
    private val dbPassword: String = config.getString("app.database.password")
    private val dbHost: String = config.getString("app.database.hostname")
    private val dbPort: String = config.getString("app.database.port")
    private val pgSchema: String = config.getString("app.database.schema")
    private val dbNameDefault: String = config.getString("app.database.database_default")

    fun connect() {
        Database.connect(
            driver = "org.postgresql.Driver",
            url = "************************************************************************",
            user = dbUser,
            password = dbPassword
        )

        transaction {
        }
    }

    fun isAppReady(): Bo<PERSON>an {
        return try {
            transaction {
                exec("SELECT 1") { rs ->
                    rs.next()
                } ?: false
            }
        } catch (_: Exception) {
            false
        }
    }
}