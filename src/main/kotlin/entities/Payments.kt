package entities

import models.PaymentStatus
import org.jetbrains.exposed.v1.core.Table
import org.jetbrains.exposed.v1.datetime.timestamp
import kotlin.time.Clock

object Payments : Table("payments") {
    val paymentId = varchar("payment_id", 64)
    val customerId = varchar("customer_id", 64)
    val customerTokenId = varchar("customer_token_id", 64)
    val amount = decimal("amount", 15, 2)
    val currency = varchar("currency", 3)
    val paymentStatus = varchar("status", 32).clientDefault { PaymentStatus.PENDING.name }
    val merchantStatus = varchar("merchant_status", 255).nullable()
    val paymentMethod = varchar("payment_method", 65) // PaymentMethod
    val canonicalName = varchar("canonical_name", 255).nullable()
    val cardType = varchar("card_type", 255).nullable()
    val cardPrefix = varchar("card_prefix", 255).nullable()
    val cardSuffix = varchar("card_suffix", 255).nullable()
    val expMonth = varchar("exp_month", 255).nullable()
    val expYear = varchar("exp_year", 255).nullable()
    val paymentInstrumentId = varchar("payment_instrument_id", 255).nullable()
    val instrumentIdentifierId = varchar("instrument_identifier_id", 255).nullable()
    val deviceFingerprint = varchar("device_fingerprint", 255).nullable()
    val createdAt = timestamp("created_at").clientDefault { Clock.System.now() }
    val updatedAt = timestamp("updated_at").clientDefault { Clock.System.now() }

    override val primaryKey = PrimaryKey(paymentId)
}