package entities

import org.jetbrains.exposed.v1.core.Table
import org.jetbrains.exposed.v1.datetime.timestamp
import kotlin.time.Clock

object PaymentEvents : Table("payment_events") {
    val transactionId = varchar("transaction_id", 64)
    val referenceId = varchar("reference_id", 128).nullable()
    val paymentId = varchar("payment_id", 64).references(Payments.paymentId)
    val amount = decimal("amount", 15, 2)
    val currency = varchar("currency", 3)
    val paymentEventType = varchar("type", 32) // PaymentEventType
    val paymentEventStatus = varchar("status", 32) // PaymentEventStatus
    val errorCode = varchar("error_code", 64).nullable()
    val errorMessage = text("error_message").nullable()
    val metadata = text("metadata").nullable()
    val createdAt = timestamp("created_at").clientDefault { Clock.System.now() }

    override val primaryKey = PrimaryKey(transactionId)
}