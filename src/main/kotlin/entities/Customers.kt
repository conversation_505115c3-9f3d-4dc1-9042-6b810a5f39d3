package entities

import models.AccountStatus
import org.jetbrains.exposed.v1.core.Table
import org.jetbrains.exposed.v1.datetime.timestamp
import kotlin.time.Clock

object Customers : Table("customers") {
    val id = varchar("id", 255)
    val processorId = varchar("processor_id", 255).uniqueIndex()
    val status = varchar("status", 42).clientDefault { AccountStatus.OPEN.name }
    val createdAt = timestamp("created_at").clientDefault { Clock.System.now() }

    override val primaryKey = PrimaryKey(id)
}