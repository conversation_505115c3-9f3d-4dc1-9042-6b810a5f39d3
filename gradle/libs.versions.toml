[versions]
appStoreServerLibrary = "3.6.0"
castle-version = "2.6.1"
cybersource-client-version = "0.0.80"
exposed = "1.0.0-rc-1"
firebaseAdmin = "9.5.0"
googleCloudBom = "26.67.0"
hikariCP = "7.0.2"
jbcrypt = "0.4"
kotlin-version = "2.2.10"
ktor-version = "3.2.3"
libphonenumber = "9.0.13"
libphonenumber-carrier = "2.13"
libphonenumber-geocoder = "3.13"
libphonenumber-prefixmapper = "3.13"
logback = "1.5.18"
logback-encoder = "8.1"
playIntegrityApi = "v1-rev20250828-2.0.0"
postgresql = "42.7.7"
postgresql-r2dbc-version = "1.0.7.RELEASE"
recaptchaPasswordHelpers = "1.0.4"
vc-update-version = "1.0.0"
smiley4-version = "5.2.0"
smiley4-schema-kenerator-version = "2.3.0"
openapi-generator-version = "7.15.0"

[libraries]
app-store-server-library = { module = "com.apple.itunes.storekit:app-store-server-library", version.ref = "appStoreServerLibrary" }
castle-java = { module = "io.castle:castle-java", version.ref = "castle-version" }
cybersource-rest-client = { module = "com.cybersource:cybersource-rest-client-java", version.ref = "cybersource-client-version" }
exposed-core = { module = "org.jetbrains.exposed:exposed-core", version.ref = "exposed" }
exposed-dao = { module = "org.jetbrains.exposed:exposed-dao", version.ref = "exposed" }
exposed-java-time = { module = "org.jetbrains.exposed:exposed-java-time", version.ref = "exposed" }
exposed-jdbc = { module = "org.jetbrains.exposed:exposed-jdbc", version.ref = "exposed" }
exposed-kotlin-datetime = { module = "org.jetbrains.exposed:exposed-kotlin-datetime", version.ref = "exposed" }
exposed-money = { module = "org.jetbrains.exposed:exposed-money", version.ref = "exposed" }
exposed-r2dbc = { module = "org.jetbrains.exposed:exposed-r2dbc", version.ref = "exposed" }
firebase-admin = { module = "com.google.firebase:firebase-admin", version.ref = "firebaseAdmin" }
google-cloud-bom = { module = "com.google.cloud:libraries-bom", version.ref = "googleCloudBom" }
google-cloud-recaptchaenterprise = { module = "com.google.cloud:google-cloud-recaptchaenterprise" }
google-play-integrity-api = { module = "com.google.apis:google-api-services-playintegrity", version.ref = "playIntegrityApi" }
hikari = { module = "com.zaxxer:HikariCP", version.ref = "hikariCP" }
jbcrypt = { module = "org.mindrot:jbcrypt", version.ref = "jbcrypt" }
libphonenumber = { module = "com.googlecode.libphonenumber:libphonenumber", version.ref = "libphonenumber" }
libphonenumber-carrier = { module = "com.googlecode.libphonenumber:carrier", version.ref = "libphonenumber-carrier" }
libphonenumber-geocoder = { module = "com.googlecode.libphonenumber:geocoder", version.ref = "libphonenumber-geocoder" }
libphonenumber-prefixmapper = { module = "com.googlecode.libphonenumber:prefixmapper", version.ref = "libphonenumber-prefixmapper" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logback" }
logback-core = { module = "ch.qos.logback:logback-core", version.ref = "logback" }
logstash-encoder = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logback-encoder" }
postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresql" }
postgresql-r2dbc = { module = "org.postgresql:r2dbc-postgresql", version.ref = "postgresql-r2dbc-version" }
recaptcha-password-check-helpers = { module = "com.google.cloud:recaptcha-password-check-helpers", version.ref = "recaptchaPasswordHelpers" }
smiley4-openapi = { module = "io.github.smiley4:ktor-openapi", version.ref = "smiley4-version" }
smiley4-swagger = { module = "io.github.smiley4:ktor-swagger-ui", version.ref = "smiley4-version" }
smiley4-schema-kenerator-core = { module = "io.github.smiley4:schema-kenerator-core", version.ref = "smiley4-schema-kenerator-version" }
smiley4-schema-kenerator-swagger = { module = "io.github.smiley4:schema-kenerator-swagger", version.ref = "smiley4-schema-kenerator-version" }

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin-version" }
kotlin-plugin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin-version" }
ktor = { id = "io.ktor.plugin", version.ref = "ktor-version" }
version-catalog-update = { id = "nl.littlerobots.version-catalog-update", version.ref = "vc-update-version" }
openapi-generator = { id = "org.openapi.generator", version.ref = "openapi-generator-version" }
