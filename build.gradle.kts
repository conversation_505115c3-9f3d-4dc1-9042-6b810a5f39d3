plugins {
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.ktor)
    alias(libs.plugins.kotlin.plugin.serialization)
    alias(libs.plugins.version.catalog.update)
    alias(libs.plugins.openapi.generator)
}

group = "com.esdiac"
version = "0.0.6"

application {
    mainClass = "io.ktor.server.cio.EngineMain"
}

repositories {
    mavenCentral()
}

dependencies {
    implementation(ktorLibs.serialization.kotlinx.json)
    implementation(ktorLibs.serialization.jackson)

    implementation(ktorLibs.client.cio)
    implementation(ktorLibs.client.contentNegotiation)
    implementation(ktorLibs.client.logging)
    implementation(ktorLibs.client.auth)
    implementation(ktorLibs.client.websockets)
    implementation(ktorLibs.client.json)

    implementation(ktorLibs.server.cio)
    implementation(ktorLibs.server.contentNegotiation)
    implementation(ktorLibs.server.callLogging)
    implementation(ktorLibs.server.sessions)
    implementation(ktorLibs.server.auth)
    implementation(ktorLibs.server.auth.jwt)
    implementation(ktorLibs.server.freemarker)
    implementation(ktorLibs.server.cors)
    implementation(ktorLibs.server.statusPages)
    implementation(ktorLibs.server.rateLimit)
    implementation(ktorLibs.server.doubleReceive)
    implementation(ktorLibs.server.callId)
    implementation(ktorLibs.server.websockets)
    implementation(ktorLibs.server.forwardedHeader)
    implementation(ktorLibs.server.metrics)
    implementation(ktorLibs.server.metrics.micrometer)
    implementation(ktorLibs.server.htmlBuilder)
    implementation(ktorLibs.server.requestValidation)

    implementation(libs.exposed.core)
    implementation(libs.exposed.dao)
    implementation(libs.exposed.jdbc)
    implementation(libs.postgresql)
    implementation(libs.exposed.money)
    implementation(libs.exposed.kotlin.datetime)
    implementation(libs.hikari)
    implementation(libs.jbcrypt)

    implementation(libs.libphonenumber)
    implementation(libs.libphonenumber.geocoder)
    implementation(libs.libphonenumber.carrier)
    implementation(libs.libphonenumber.prefixmapper)

    implementation(libs.logback.core)
    implementation(libs.logback.classic)
    implementation(libs.logstash.encoder)

    implementation(libs.smiley4.swagger)
    implementation(libs.smiley4.openapi)
    implementation(libs.smiley4.schema.kenerator.core)
    implementation(libs.smiley4.schema.kenerator.swagger)

    implementation(libs.cybersource.rest.client)

    // Testing dependencies
    testImplementation(ktorLibs.server.test.host)
    testImplementation(libs.junit.jupiter.api)
    testImplementation(libs.junit.jupiter.engine)
    testImplementation(libs.junit.jupiter.params)
    testImplementation(libs.mockk)
    testImplementation(libs.kotlin.test.junit5)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(libs.h2.database)
    testImplementation(libs.testcontainers.junit.jupiter)
    testImplementation(libs.testcontainers.postgresql)
}

tasks.test {
    useJUnitPlatform()
}

repositories {
    mavenCentral()

    maven {
        url = uri("https://esdiac-584252333092.d.codeartifact.us-west-2.amazonaws.com/maven/esdiac/")
        credentials {
            username = "aws"
            password = System.getenv("CODEARTIFACT_AUTH_TOKEN")
        }
    }
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

kotlin {
    sourceSets.all {
        languageSettings.optIn("kotlin.time.ExperimentalTime")
    }
}

tasks.withType<Zip> {
    isZip64 = true
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile>().configureEach {
    compilerOptions {
        jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_21)
    }
}

openApiGenerate {
    generatorName = "kotlin"
    inputSpec = "$projectDir/docs/openapi.yaml"
    outputDir = layout.buildDirectory.dir("generated").get().asFile.path

    id = "purchase-api"
    groupId = project.group.toString()
    version = project.version.toString()

    apiPackage = "com.esdiac.purchase.api"
    modelPackage = "com.esdiac.purchase.model"
    invokerPackage = "com.esdiac.purchase.infrastructure"

    generateModelTests = false
    generateApiTests = false
    generateModelDocumentation = true
    generateApiDocumentation = true

    configOptions = mapOf(
        "serializationLibrary" to "kotlinx_serialization",
        "useCoroutines" to "true",
        "packageName" to "com.esdiac.purchase",
    )

    additionalProperties = mapOf()

    typeMappings = mapOf()

    importMappings = mapOf()
}