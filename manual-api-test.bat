@echo off
echo Manual API Testing Script
echo This will test your API endpoints directly without <PERSON>radle dependencies

echo.
echo Starting your application first...
echo Please make sure your application is running on localhost:8080
echo.

echo Testing API endpoints manually...
echo.

echo 1. Testing Health Check
curl -X GET http://localhost:8080/healthz
echo.
echo.

echo 2. Testing Payment Session Creation (CREDIT_CARD)
curl -X POST http://localhost:8080/api/v1/payment-sessions ^
  -H "Content-Type: application/json" ^
  -d "{\"paymentMethod\": \"CREDIT_CARD\", \"deviceType\": \"WEB\"}"
echo.
echo.

echo 3. Testing Payment Session Creation (VAULT)
curl -X POST http://localhost:8080/api/v1/payment-sessions ^
  -H "Content-Type: application/json" ^
  -d "{\"paymentMethod\": \"VAULT\", \"deviceType\": \"ANDROID\"}"
echo.
echo.

echo 4. Testing Invalid Payment Method
curl -X POST http://localhost:8080/api/v1/payment-sessions ^
  -H "Content-Type: application/json" ^
  -d "{\"paymentMethod\": \"INVALID_METHOD\", \"deviceType\": \"WEB\"}"
echo.
echo.

echo 5. Testing Get All Payments
curl -X GET http://localhost:8080/api/v1/payments
echo.
echo.

echo 6. Testing Get Non-existent Payment
curl -X GET http://localhost:8080/api/v1/payments/non-existent-payment
echo.
echo.

echo 7. Testing Get Non-existent Customer
curl -X GET http://localhost:8080/api/v1/customers/non-existent-customer
echo.
echo.

echo 8. Testing Payment Authorization (should fail - no valid session)
curl -X POST http://localhost:8080/api/v1/payments/test-payment/authorize ^
  -H "Content-Type: application/json" ^
  -d "{\"amount\": \"100.00\", \"currency\": \"USD\", \"customerDetail\": {\"id\": \"test\", \"email\": \"<EMAIL>\"}}"
echo.
echo.

echo Manual testing completed!
echo.
echo Results Summary:
echo - Health check should return 200 OK
echo - CREDIT_CARD payment session should return 200 OK with payment details
echo - VAULT payment session might return 500 if not implemented
echo - Invalid payment method should return 400 Bad Request
echo - Get payments should return 200 OK with empty array []
echo - Non-existent resources should return 404 Not Found
echo - Authorization without valid session should return 404 Not Found
echo.

pause
