apiVersion: apps/v1
kind: Deployment
metadata:
  name: purchase-api
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: purchase-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  template:
    metadata:
      labels:
        app: purchase-api
    spec:
      restartPolicy: Always
      containers:
        - name: purchase-api
          image: 584252333092.dkr.ecr.us-west-2.amazonaws.com/purchase-api:0.0.6
          imagePullPolicy: Always
          env:
            - name: POSTGRES_HOSTNAME
              value: "esdiac-db-test.cpkx1qaixizr.us-west-2.rds.amazonaws.com"
            - name: POSTGRES_PORT
              value: "5432"
            - name: POSTGRES_USER
              value: ""
            - name: POSTGRES_PASSWORD
              value: ""
            - name: CYBERSOURCE_MERCHANT_ID
              value: ""
            - name: CYBERSOURCE_ORGANIZATION_ID
              value: ""
            - name: CYBERSOURCE_RUN_ENVIRONMENT
              value: "apitest.cybersource.com"
            - name: CYBERSOURCE_FLEX_ENVIRONMENT
              value: "testflex.cybersource.com"
            - name: CYBERSOURCE_PROFILE_ID
              value: ""
            - name: CYBERSOURCE_PROFILER_URL
              value: "content1.esdiacapi.com"
            - name: CYBERSOURCE_KEY_ID
              value: ""
            - name: CYBERSOURCE_KEY_SECRET
              value: ""
            - name: CYBERSOURCE_MICRO_FORM_ORIGIN
#              value: "http://localhost:8181"
              value: "https://app.esdiac.dev"
            - name: APPLE_ISSUER_ID
              value: ""
            - name: APPLE_KEY_ID
              value: ""
            - name: APPLE_BUNDLE_ID
              value: ""
            - name: APPLE_APP_APPLE_ID
              value: ""
            - name: APPLE_PRIVATE_KEY
              value: ""
            - name: APPLE_CAS
              value: ""
            - name: APPLE_PAY_MERCHANT_IDENTIFIER
              value: ""
            - name: APPLE_PAY_KEY_PATH
              value: ""
            - name: APPLE_PAY_CERTIFICATE_PATH
              value: ""
            - name: GOOGLE_PLAY_RESOURCE_URL
              value: ""
            - name: GOOGLE_PLAY_SERVICE_ACCOUNT_EMAIL
              value: ""
            - name: GOOGLE_PLAY_APPLICATION_NAME
              value: ""
            - name: PAYPAL_CHECKOUT_CLIENT_ID
              value: ""
            - name: PAYPAL_CLIENT_SECRET
              value: ""
            - name: HOST
              value: "0.0.0.0"
            - name: PORT
              value: "80"
            - name: ENVIRONMENT
              value: "release"
          ports:
            - containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: "/healthz"
              port: 80
          readinessProbe:
            httpGet:
              path: "/ready"
              port: 80