openapi: 3.1.0
info:
  title: Purchase API
  contact:
    email: <EMAIL>
  version: 1.0.0
externalDocs:
  url: /
servers:
  - url: http://purchase-api
    description: Production server
  - url: http://purchase-api-
    description: Staging server
  - url: http://localhost:5010
    description: Local server
tags:
  - name: Health
    description: Health check
  - name: Customer
    description: Customer operations
  - name: Payment
    description: Payment operations
paths:
  /healthz:
    get:
      tags:
        - Health
      description: Health check
      operationId: healthCheck
      parameters: []
      responses:
        "200":
          description: Application is healthy
          headers: {}
      deprecated: false
  /ready:
    get:
      tags:
        - Health
      description: Ready check
      operationId: readyCheck
      parameters: []
      responses:
        "200":
          description: Application is ready to receive traffic
          headers: {}
      deprecated: false
  /api/v1/customers/{id}:
    get:
      tags:
        - Payment
      description: Get customer by id
      operationId: getCustomer
      parameters:
        - name: id
          in: path
          description: Customer id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Customer found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Customer"
        "403":
          description: Customer account is locked
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "404":
          description: Customer not found
          headers: {}
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
    delete:
      tags:
        - Payment
      description: Delete a customer by id
      operationId: deleteCustomer
      parameters:
        - name: id
          in: path
          description: The customer id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      responses:
        "204":
          description: Customer deleted
          headers: {}
        "404":
          description: Customer not found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "403":
          description: Customer account is locked
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "409":
          description: Duplicate transaction
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
  /api/v1/customers/{id}/payment-instruments/{paymentInstrumentId}:
    put:
      tags:
        - Payment
      description: Update a payment instrument billing address
      operationId: updatePaymentInstrument
      parameters:
        - name: id
          in: path
          description: The customer id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
        - name: paymentInstrumentId
          in: path
          description: The payment instrument id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      requestBody:
        description: Payment instrument update request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Request.UpdatePaymentInstrument"
        required: false
      responses:
        "200":
          description: Payment instrument updated
          headers: {}
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PaymentInstrument"
        "403":
          description: Customer account is locked
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "404":
          description: Payment instrument not found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "400":
          description: Invalid request data
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "409":
          description: Duplicate transaction
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
    delete:
      tags:
        - Payment
      description: Delete a payment instrument by id
      operationId: deletePaymentInstrument
      parameters:
        - name: id
          in: path
          description: The customer id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
        - name: paymentInstrumentId
          in: path
          description: The payment instrument id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      responses:
        "204":
          description: Customer deleted
          headers: {}
        "403":
          description: Customer account is locked
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "404":
          description: Payment instrument not found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "400":
          description: Invalid request data
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "409":
          description: Duplicate transaction
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
    patch:
      tags:
        - Payment
      description: Update a payment instrument as default
      operationId: updateDefaultPaymentInstrument
      parameters:
        - name: id
          in: path
          description: The customer id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
        - name: paymentInstrumentId
          in: path
          description: The payment instrument id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      requestBody:
        description: Payment instrument update request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Request.UpdateDefaultPaymentInstrument"
        required: false
      responses:
        "200":
          description: Payment instrument updated
          headers: {}
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PaymentInstrument"
        "403":
          description: Customer account is locked
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "404":
          description: Payment instrument not found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "400":
          description: Invalid request data
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "409":
          description: Duplicate transaction
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
  /api/v1/payment-sessions:
    post:
      tags:
        - Payment
      description: Create a payment session
      operationId: createPaymentSession
      parameters: []
      requestBody:
        description: Payment session request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Request.PaymentSession"
        required: false
      responses:
        "200":
          description: Payment session created
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Response.PaymentSession"
        "400":
          description: Invalid request
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
  /api/v1/payments:
    get:
      tags:
        - Payment
      description: Get all payments
      operationId: getPayments
      parameters: []
      responses:
        "200":
          description: Payments found
          headers: {}
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Response.Payment"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
  /api/v1/payments/{id}:
    get:
      tags:
        - Payment
      description: Get payment by id
      operationId: getPayment
      parameters:
        - name: id
          in: path
          description: Payment id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Payment found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Response.Payment"
        "404":
          description: Payment not found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
  /api/v1/payments/{id}/authorize:
    post:
      tags:
        - Payment
      description: Authorize a payment
      operationId: authorizePayment
      parameters:
        - name: id
          in: path
          description: The payment id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      requestBody:
        description: Payment authorize request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Request.Authorize"
        required: false
      responses:
        "200":
          description: Payment authorized
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Response.Authorized"
        "400":
          description: Invalid request
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "403":
          description: Payment is not authorized for this customer
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "404":
          description: Payment not found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "409":
          description: Payment already authorized
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
  /api/v1/payments/{id}/capture:
    post:
      tags:
        - Payment
      description: Capture a payment
      operationId: capturePayment
      parameters:
        - name: id
          in: path
          description: The payment id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      requestBody:
        description: Payment capture request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Request.CreditCardCapture"
        required: false
      responses:
        "200":
          description: Payment captured
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Response.Captured"
        "400":
          description: Invalid request
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "404":
          description: Payment not found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "409":
          description: Payment already captured
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
  /api/v1/payments/{id}/reverse:
    post:
      tags:
        - Payment
      description: Reverse a payment
      operationId: reversePayment
      parameters:
        - name: id
          in: path
          description: The payment id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      requestBody:
        description: Payment reverse request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Request.CreditCardReverse"
        required: false
      responses:
        "200":
          description: Payment reversed
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Response.Reversed"
        "400":
          description: Invalid request
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "404":
          description: Payment not found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "409":
          description: Payment already reversed
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
  /api/v1/payments/{id}/void:
    post:
      tags:
        - Payment
      description: Void a payment
      operationId: voidPayment
      parameters:
        - name: id
          in: path
          description: The payment id
          required: true
          deprecated: false
          explode: false
          schema:
            type: string
      requestBody:
        description: Payment void request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Request.CreditCardVoid"
        required: false
      responses:
        "200":
          description: Payment voided
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Response.Voided"
        "400":
          description: Invalid request
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "404":
          description: Payment not found
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "409":
          description: Payment already void
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
        "500":
          description: Internal server error
          headers: {}
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AppError"
      deprecated: false
components:
  schemas:
    Address:
      type: object
      properties:
        iso2:
          type: string
        state:
          type: string
        city:
          type: string
        zipCode:
          type: string
        streetAddress:
          type: string
        extendedAddress:
          type: string
      required:
        - city
        - extendedAddress
        - iso2
        - state
        - streetAddress
        - zipCode
    PaymentInstrument:
      type: object
      properties:
        id:
          type: string
        paymentMethod:
          type: string
        canonicalName:
          type: string
        brand:
          type: string
        last4:
          type: string
        isDefault:
          type: boolean
      required:
        - brand
        - canonicalName
        - id
        - isDefault
        - last4
        - paymentMethod
    Customer:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        billingAddress:
          $ref: "#/components/schemas/Address"
        paymentInstruments:
          type: array
          items:
            $ref: "#/components/schemas/PaymentInstrument"
      required:
        - email
        - id
        - paymentInstruments
    AppError:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
      required:
        - error
        - message
    BillingAddress:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        iso2:
          type: string
        state:
          type: string
        city:
          type: string
        zipCode:
          type: string
        streetAddress:
          type: string
        extendedAddress:
          type: string
      required:
        - city
        - extendedAddress
        - firstName
        - iso2
        - lastName
        - state
        - streetAddress
        - zipCode
    Request.UpdatePaymentInstrument:
      type: object
      properties:
        billingAddress:
          $ref: "#/components/schemas/BillingAddress"
      required:
        - billingAddress
    Request.UpdateDefaultPaymentInstrument:
      type: object
      properties:
        isDefault:
          type: boolean
      required:
        - isDefault
    Request.PaymentSession:
      type: object
      properties:
        paymentMethod:
          type: string
        deviceType:
          type: string
      required:
        - deviceType
        - paymentMethod
    Response.PaymentSession:
      type: object
      properties:
        paymentId:
          type: string
        paymentSignature:
          type: string
        deviceType:
          type: string
        paymentMethod:
          type: string
        organizationId:
          type: string
        merchantId:
          type: string
      required:
        - deviceType
        - merchantId
        - organizationId
        - paymentId
        - paymentMethod
        - paymentSignature
    Response.Payment:
      type: object
      properties:
        paymentId:
          type: string
        customerId:
          type: string
        amount:
          type: string
        currency:
          type: string
        status:
          type: string
        paymentMethod:
          type: string
        createdAt:
          type: string
        canonicalName:
          type: string
        cardType:
          type: string
        events:
          type: array
          items:
            $ref: "#/components/schemas/Response.PaymentEvent"
      required:
        - amount
        - createdAt
        - currency
        - customerId
        - events
        - paymentId
        - paymentMethod
        - status
    Response.PaymentEvent:
      type: object
      properties:
        referenceId:
          type: string
        paymentEventType:
          type: string
        paymentEventStatus:
          type: string
        amount:
          type: string
        currency:
          type: string
        createdAt:
          type: string
        errorCode:
          type: string
        errorMessage:
          type: string
        metadata:
          type: string
      required:
        - amount
        - createdAt
        - currency
        - paymentEventStatus
        - paymentEventType
    Request.CustomerDetail:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        phone:
          type: string
        firstName:
          type: string
        lastName:
          type: string
      required:
        - email
        - firstName
        - id
        - lastName
    Request.CreditCardDetail:
      type: object
      properties:
        paymentData:
          type: string
        storeInVault:
          type: boolean
      required:
        - paymentData
        - storeInVault
    Request.VaultDetails:
      type: object
      properties:
        paymentInstrumentId:
          type: string
      required:
        - paymentInstrumentId
    DeviceFingerprint:
      type: object
      properties:
        sessionId:
          type: string
        ipAddress:
          type: string
        hostname:
          type: string
        userAgent:
          type: string
      required:
        - hostname
        - ipAddress
        - sessionId
        - userAgent
    Request.Authorize:
      type: object
      properties:
        amount:
          type: string
        currency:
          type: string
        customerDetail:
          $ref: "#/components/schemas/Request.CustomerDetail"
        creditCardDetail:
          $ref: "#/components/schemas/Request.CreditCardDetail"
        vaultDetails:
          $ref: "#/components/schemas/Request.VaultDetails"
        billingAddress:
          $ref: "#/components/schemas/BillingAddress"
        deviceFingerprint:
          $ref: "#/components/schemas/DeviceFingerprint"
      required:
        - amount
        - currency
        - customerDetail
    Response.Authorized:
      type: object
      properties:
        date:
          type: string
        paymentId:
          type: string
        amount:
          type: string
        currency:
          type: string
        canonicalName:
          type: string
        cardType:
          type: string
      required:
        - amount
        - canonicalName
        - cardType
        - currency
        - date
        - paymentId
    Request.CreditCardCapture:
      type: object
      properties:
        paymentId:
          type: string
      required:
        - paymentId
    Response.Captured:
      type: object
      properties:
        date:
          type: string
        paymentId:
          type: string
        amount:
          type: string
        currency:
          type: string
        canonicalName:
          type: string
        cardType:
          type: string
      required:
        - amount
        - canonicalName
        - cardType
        - currency
        - date
        - paymentId
    Request.CreditCardReverse:
      type: object
      properties:
        paymentId:
          type: string
        reason:
          type: string
      required:
        - paymentId
        - reason
    Response.Reversed:
      type: object
      properties:
        date:
          type: string
        paymentId:
          type: string
        amount:
          type: string
        currency:
          type: string
      required:
        - amount
        - currency
        - date
        - paymentId
    Request.CreditCardVoid:
      type: object
      properties:
        paymentId:
          type: string
      required:
        - paymentId
    Response.Voided:
      type: object
      properties:
        date:
          type: string
        paymentId:
          type: string
        amount:
          type: string
        currency:
          type: string
      required:
        - amount
        - currency
        - date
        - paymentId
  examples: {}
webhooks: {}