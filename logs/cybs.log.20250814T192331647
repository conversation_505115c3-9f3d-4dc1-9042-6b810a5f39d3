2025-08-14 19:14:27.463 eventLoopGroupProxy-4-2 FILESTART> CYBERSOURCE LOG FILE

2025-08-14 19:14:27.464 eventLoopGroupProxy-4-2 TRANSTART> =======================================
2025-08-14 19:14:27.465 eventLoopGroupProxy-4-2 MERCHCFG > merchantID=esd000000026463, keysDirectory=C:/Users/<USER>/Desktop/Esdiac/purchase/keys, keyAlias=serialnumber=1755189230278055332140,cn=esd000000026463, keyPassword=(masked), sendToProduction=false, sendToAkamai=true, targetAPIVersion=1.129, keyFilename=esd000000026463.p12, serverURL=(null), namespaceURI=(null), enableLog=true, logDirectory=logs, logFilename=cybs.log, logMaximumSize=10485760, customHttpClass=(null), customHttpClassEnabled=false, useHttpClient=true, useHttpClientWithConnectionPool=false, enableJdkCert=false, enableCacert=false, allowRetry=true, retryCount=3, retryInterval=1000, timeout=130000, socketTimeoutMs=*********, evictThreadSleepTimeMs=0, useSignAndEncrypted=false, certificateCacheEnabled=true, merchantConfigCacheEnabled=false
2025-08-14 19:14:27.505 eventLoopGroupProxy-4-2 REQUEST  > UUID   >  ********-618a-4224-bf8e-814d74371338
Input request is
======================================= 
merchantReferenceCode=ORDER-2025-010
ccAuthService_run=true
card_accountNumber=5555xxxxxxxx4444
card_expirationMonth=xx
card_expirationYear=xxxx
card_cvNumber=xxx
purchaseTotals_currency=USD
purchaseTotals_grandTotalAmount=300.00
billTo_firstName=xxxx
billTo_lastName=xxx
billTo_street1=12xxxxxxxxxxxet
billTo_city=New York
billTo_state=NY
billTo_postalCode=10001
billTo_country=US
billTo_email=johnxxxxxxxxxxxx.com
billTo_phoneNumber=+1xxxxxxxxxxx67
ccAuthService_commerceIndicator=internet
merchantID=esd000000026463
clientLibrary=Java NVP/6.2.15
clientLibraryVersion=6.2.15
clientEnvironment=Windows 11/10.0/Eclipse Adoptium/17.0.15

2025-08-14 19:14:27.521 eventLoopGroupProxy-4-2 INFO     > Client, End of soapWrap   75ms
2025-08-14 19:14:27.525 eventLoopGroupProxy-4-2 INFO     > Loading the certificate from p12 file 
2025-08-14 19:14:27.601 eventLoopGroupProxy-4-2 EXCEPTION> ClientException details:
innerException: 

