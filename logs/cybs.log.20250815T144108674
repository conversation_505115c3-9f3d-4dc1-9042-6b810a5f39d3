2025-08-15 14:35:18.993 eventLoopGroupProxy-4-1 FILESTART> CYBERSOURCE LOG FILE

2025-08-15 14:35:18.995 eventLoopGroupProxy-4-1 TRANSTART> =======================================
2025-08-15 14:35:18.997 eventLoopGroupProxy-4-1 MERCHCFG > merchantID=esd000000026463, keysDirectory=C:/Users/<USER>/Desktop/Esdiac/purchase/keys, keyAlias=serialnumber=1755189230278055332140,cn=esd000000026463, keyPassword=(masked), sendToProduction=false, sendToAkamai=true, targetAPIVersion=1.129, keyFilename=esd000000026463.p12, serverURL=(null), namespaceURI=(null), enableLog=true, logDirectory=logs, logFilename=cybs.log, logMaximumSize=10485760, customHttpClass=(null), customHttpClassEnabled=false, useHttpClient=true, useHttpClientWithConnectionPool=false, enableJdkCert=false, enableCacert=false, allowRetry=true, retryCount=3, retryInterval=1000, timeout=130000, socketTimeoutMs=*********, evictThreadSleepTimeMs=0, useSignAndEncrypted=false, certificateCacheEnabled=true, merchantConfigCacheEnabled=false
2025-08-15 14:35:19.034 eventLoopGroupProxy-4-1 REQUEST  > UUID   >  e5013c6e-c19b-45f6-bba9-0b357cb5b1d8
Input request is
======================================= 
merchantReferenceCode=ORDER-2025-009
ccAuthService_run=true
card_accountNumber=5555xxxxxxxx4444
card_expirationMonth=xx
card_expirationYear=xxxx
card_cvNumber=xxx
purchaseTotals_currency=USD
purchaseTotals_grandTotalAmount=300.00
billTo_firstName=xxxx
billTo_lastName=xxx
billTo_street1=12xxxxxxxxxxxet
billTo_city=New York
billTo_state=NY
billTo_postalCode=10001
billTo_country=US
billTo_email=johnxxxxxxxxxxxx.com
billTo_phoneNumber=+1xxxxxxxxxxx67
ccAuthService_commerceIndicator=internet
merchantID=esd000000026463
clientLibrary=Java NVP/6.2.15
clientLibraryVersion=6.2.15
clientEnvironment=Windows 11/10.0/Eclipse Adoptium/17.0.15

2025-08-15 14:35:19.046 eventLoopGroupProxy-4-1 INFO     > Client, End of soapWrap   58ms
2025-08-15 14:35:20.406 eventLoopGroupProxy-4-1 INFO     > Loading the certificate from p12 file 
2025-08-15 14:35:20.906 eventLoopGroupProxy-4-1 EXCEPTION> ClientException details:
innerException: 

