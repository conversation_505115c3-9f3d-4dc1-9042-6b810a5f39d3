# Environment Setup Guide

Based on your `application.conf` and API structure, here's the complete environment setup needed for testing.

## 🔧 **Required Environment Variables**

### **Cybersource Configuration (Required)**
```bash
# Core Cybersource Settings
export CYBERSOURCE_MERCHANT_ID="your_merchant_id"
export CYBERSOURCE_ORGANIZATION_ID="your_organization_id"
export CYBERSOURCE_RUN_ENVIRONMENT="sandbox"  # or "production"
export CYBERSOURCE_FLEX_ENVIRONMENT="sandbox"  # or "production"

# Authentication
export CYBERSOURCE_KEY_ID="your_key_id"
export CYBERSOURCE_KEY_SECRET="your_key_secret"

# Profiler and Microform
export CYBERSOURCE_PROFILER_URL="https://h.online-metrix.net/fp/tags.js?org_id=your_org&session_id=session"
export CYBERSOURCE_PROFILE_ID="your_profile_id"
export CYBERSOURCE_MICRO_FORM_ORIGIN="http://localhost:8080"  # CRITICAL for payment sessions
```

### **Database Configuration (Optional for Testing)**
```bash
# PostgreSQL Settings (uses defaults if not set)
export POSTGRES_HOSTNAME="localhost"
export POSTGRES_PORT="5432"
export POSTGRES_USER="your_db_user"
export POSTGRES_PASSWORD="your_db_password"
```

### **Server Configuration (Optional)**
```bash
# Server Settings (uses defaults if not set)
export HOSTNAME="localhost"
export PORT="8080"
```

### **Apple Pay Configuration (Optional)**
```bash
export APPLE_ISSUER_ID="your_issuer_id"
export APPLE_KEY_ID="your_key_id"
export APPLE_BUNDLE_ID="com.your.app"
export APPLE_APP_APPLE_ID="your_app_id"
export APPLE_PRIVATE_KEY="your_private_key"
export APPLE_CAS="your_cas"
export APPLE_PAY_MERCHANT_IDENTIFIER="merchant.your.identifier"
export APPLE_PAY_KEY_PATH="path/to/key"
export APPLE_PAY_CERTIFICATE_PATH="path/to/cert"
```

### **Google Play Configuration (Optional)**
```bash
export GOOGLE_PLAY_RESOURCE_URL="https://your.googleapis.com"
export GOOGLE_PLAY_SERVICE_ACCOUNT_EMAIL="<EMAIL>"
export GOOGLE_PLAY_APPLICATION_NAME="your-app-name"
```

### **PayPal Configuration (Optional)**
```bash
export PAYPAL_CHECKOUT_CLIENT_ID="your_paypal_client_id"
export PAYPAL_CLIENT_SECRET="your_paypal_client_secret"
```

## 🚀 **Quick Setup for Testing**

### **Minimum Required Setup**
```bash
# Set these for basic payment session testing
export CYBERSOURCE_MERCHANT_ID="test_merchant"
export CYBERSOURCE_ORGANIZATION_ID="test_org"
export CYBERSOURCE_RUN_ENVIRONMENT="sandbox"
export CYBERSOURCE_FLEX_ENVIRONMENT="sandbox"
export CYBERSOURCE_KEY_ID="test_key_id"
export CYBERSOURCE_KEY_SECRET="test_key_secret"
export CYBERSOURCE_PROFILE_ID="test_profile"
export CYBERSOURCE_PROFILER_URL="https://h.online-metrix.net/fp/tags.js?org_id=test&session_id=test"
export CYBERSOURCE_MICRO_FORM_ORIGIN="http://localhost:8080"
```

### **Windows Setup**
```cmd
set CYBERSOURCE_MERCHANT_ID=test_merchant
set CYBERSOURCE_ORGANIZATION_ID=test_org
set CYBERSOURCE_RUN_ENVIRONMENT=sandbox
set CYBERSOURCE_FLEX_ENVIRONMENT=sandbox
set CYBERSOURCE_KEY_ID=test_key_id
set CYBERSOURCE_KEY_SECRET=test_key_secret
set CYBERSOURCE_PROFILE_ID=test_profile
set CYBERSOURCE_PROFILER_URL=https://h.online-metrix.net/fp/tags.js?org_id=test&session_id=test
set CYBERSOURCE_MICRO_FORM_ORIGIN=http://localhost:8080
```

### **PowerShell Setup**
```powershell
$env:CYBERSOURCE_MERCHANT_ID="test_merchant"
$env:CYBERSOURCE_ORGANIZATION_ID="test_org"
$env:CYBERSOURCE_RUN_ENVIRONMENT="sandbox"
$env:CYBERSOURCE_FLEX_ENVIRONMENT="sandbox"
$env:CYBERSOURCE_KEY_ID="test_key_id"
$env:CYBERSOURCE_KEY_SECRET="test_key_secret"
$env:CYBERSOURCE_PROFILE_ID="test_profile"
$env:CYBERSOURCE_PROFILER_URL="https://h.online-metrix.net/fp/tags.js?org_id=test&session_id=test"
$env:CYBERSOURCE_MICRO_FORM_ORIGIN="http://localhost:8080"
```

## 🧪 **Testing Your Setup**

### **1. Test Environment Variables**
```bash
# Check if variables are set
echo $CYBERSOURCE_MERCHANT_ID
echo $CYBERSOURCE_MICRO_FORM_ORIGIN
```

### **2. Test Payment Session Creation**
```bash
curl -X POST http://localhost:8080/api/v1/payment-sessions \
  -H "Content-Type: application/json" \
  -d '{
    "paymentMethod": "CREDIT_CARD",
    "deviceType": "WEB"
  }'
```

**Expected Response:**
```json
{
  "paymentId": "uuid-here",
  "paymentSignature": "jwt-token-here",
  "deviceType": "WEB",
  "paymentMethod": "CREDIT_CARD",
  "organizationId": "your-org-id",
  "merchantId": "your-merchant-id"
}
```

### **3. Test All Endpoints**
```bash
# Run comprehensive tests
./gradlew test --tests "AccurateApiTest"

# Run specific test categories
./gradlew test --tests "AccurateApiTest.PaymentSessionTests"
./gradlew test --tests "AccurateApiTest.PaymentInquiryTests"
```

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **1. "Target Origin[0] malformed URL" Error**
- **Cause**: `CYBERSOURCE_MICRO_FORM_ORIGIN` not set or invalid
- **Solution**: Set to valid URL: `http://localhost:8080`

#### **2. Payment Session Creation Fails**
- **Cause**: Missing Cybersource credentials
- **Solution**: Set all required `CYBERSOURCE_*` variables

#### **3. Database Connection Errors**
- **Cause**: PostgreSQL not running or wrong credentials
- **Solution**: Start PostgreSQL or use H2 for testing

#### **4. 500 Internal Server Error**
- **Cause**: Missing required environment variables
- **Solution**: Check application logs and set missing variables

### **Debug Commands:**
```bash
# Check application logs
tail -f logs/cybs.log

# Test with verbose output
./gradlew test --info --tests "AccurateApiTest"

# Check environment in application
curl -X GET http://localhost:8080/healthz
```

## 📋 **Environment Validation Checklist**

- [ ] `CYBERSOURCE_MERCHANT_ID` set
- [ ] `CYBERSOURCE_ORGANIZATION_ID` set
- [ ] `CYBERSOURCE_RUN_ENVIRONMENT` set to "sandbox"
- [ ] `CYBERSOURCE_FLEX_ENVIRONMENT` set to "sandbox"
- [ ] `CYBERSOURCE_KEY_ID` set
- [ ] `CYBERSOURCE_KEY_SECRET` set
- [ ] `CYBERSOURCE_PROFILE_ID` set
- [ ] `CYBERSOURCE_PROFILER_URL` set
- [ ] `CYBERSOURCE_MICRO_FORM_ORIGIN` set to "http://localhost:8080"
- [ ] Application starts without errors
- [ ] Payment session creation works
- [ ] Health check endpoint responds
- [ ] Database connection established (if using PostgreSQL)

## 🎯 **Production vs Sandbox**

### **Sandbox (Testing)**
```bash
export CYBERSOURCE_RUN_ENVIRONMENT="sandbox"
export CYBERSOURCE_FLEX_ENVIRONMENT="sandbox"
export CYBERSOURCE_MICRO_FORM_ORIGIN="http://localhost:8080"
```

### **Production**
```bash
export CYBERSOURCE_RUN_ENVIRONMENT="production"
export CYBERSOURCE_FLEX_ENVIRONMENT="production"
export CYBERSOURCE_MICRO_FORM_ORIGIN="https://your-production-domain.com"
```

## 📝 **Notes**

1. **Required vs Optional**: Only Cybersource variables are required for basic testing
2. **Security**: Never commit real credentials to version control
3. **Testing**: Use sandbox environment for all testing
4. **Microform Origin**: Must match your application's URL exactly
5. **Database**: Application can run without PostgreSQL for basic API testing

This setup will ensure all your tests pass and your API functions correctly!
