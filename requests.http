
### Create customer
POST localhost:8080/api/v1/customers
Content-Type: application/json

{
  "customerId": "37b8a95b-d8dc-4ae1-b955-f3be5e7449d3",
  "customerEmail": "<EMAIL>"
}

### get customer
GET localhost:8080/api/v1/customers/37b8a95b-d8dc-4ae1-b955-f3be5e7449d3

### delete customer
DELETE localhost:8080/api/v1/customers/3D8B721F93B781CFE063AF598E0A6D00

### update customer payment methods
PATCH localhost:8080/api/v1/customers/3D8B721F93B781CFE063AF598E0A6D00/payment-instruments/3D857A388AE94FCCE063AF598E0A560B
Content-Type: application/json

{
  "isDefault": true
}

### delete customer payment methods
DELETE localhost:8080/api/v1/customers/3D8B721F93B781CFE063AF598E0A6D00/payment-instruments/3D8B7123F1C0C787E063AF598E0AACEC


### init payment
POST localhost:8080/api/v1/pay/init
Content-Type: application/json

{
  "customerId": "a3757ab0-6047-45b4-99ee-338791c05531",
  "customerTokenId": "3D8B721F93B781CFE063AF598E0A6D00",
  "amount": "10.00",
  "currency": "USD",
  "deviceType": "web"
}

### authorize
POST localhost:8080/api/v1/pay/authorize
Content-Type: application/json

{
  "paymentId": "",
  "paymentData": "",
  "storeInVault": false,
  "billingAddress": {},
  "deviceFingerprint": {}
}

###
GET localhost:8080/api/v1/payments

###
GET localhost:8080/api/v1/payments/6cb23549-8abc-46c8-bfb2-3a6bc291dcce