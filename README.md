# Purchase API Documentation

A comprehensive payment processing API built with Kotlin/Ktor and integrated with Cybersource for secure payment transactions.

## Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [Base URL](#base-url)
- [API Endpoints](#api-endpoints)
  - [Payment Sessions](#payment-sessions)
  - [Payment Operations](#payment-operations)
  - [Customer Management](#customer-management)
  - [Payment Inquiry](#payment-inquiry)
- [Request/Response Examples](#requestresponse-examples)
- [Error Handling](#error-handling)
- [Testing](#testing)
- [Deployment](#deployment)

## Overview

The Purchase API provides a complete payment processing solution with the following capabilities:

- **Payment Session Management**: Create secure payment sessions for different payment methods
- **Payment Authorization**: Authorize payments using credit cards, vault, or default vault
- **Payment Capture**: Capture authorized payments
- **Payment Reversal**: Reverse authorized payments
- **Payment Void**: Void captured payments
- **Customer Management**: Manage customer profiles and payment instruments
- **Payment Inquiry**: Query payment status and history

### Supported Payment Methods
- `CREDIT_CARD`: Direct credit card payments with tokenization
- `VAULT`: Payments using stored payment instruments
- `DEFAULT_VAULT`: Payments using customer's default payment instrument
- `PAYPAL`: PayPal payments (coming soon)
- `GOOGLE_PAY`: Google Pay payments (coming soon)
- `APPLE_PAY`: Apple Pay payments (coming soon)

### Supported Device Types
- `WEB`: Web browser payments
- `ANDROID`: Android mobile app payments
- `IOS`: iOS mobile app payments

## Authentication

The API uses Cybersource authentication. Ensure your credentials are properly configured in the environment.

## Base URL

```
Production: https://your-production-domain.com/api/v1
Development: https://your-dev-domain.com/api/v1
Local: http://localhost:8080/api/v1
```

## API Endpoints

### Payment Sessions

#### Create Payment Session
Creates a secure payment session for processing payments.

**Endpoint:** `POST /payment-sessions`

**Request Body:**
```json
{
  "paymentMethod": "CREDIT_CARD",
  "deviceType": "WEB"
}
```

**Response:**
```json
{
  "paymentId": "payment_123456789",
  "paymentSignature": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "deviceType": "WEB",
  "paymentMethod": "CREDIT_CARD",
  "organizationId": "org_123",
  "merchantId": "merchant_123"
}
```

**Status Codes:**
- `200 OK`: Payment session created successfully
- `400 Bad Request`: Invalid payment method or device type
- `500 Internal Server Error`: Server error

### Payment Operations

#### Authorize Payment
Authorizes a payment using the specified payment method.

**Endpoint:** `POST /payments/{id}/authorize`

**Request Body (Credit Card):**
```json
{
  "amount": "100.00",
  "currency": "USD",
  "customerDetail": {
    "firstName": "John",
    "lastName": "Doe",
    "id": "customer_123",
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "creditCardDetail": {
    "paymentData": "jwt_token_from_flex",
    "storeInVault": true
  },
  "billingAddress": {
    "firstName": "John",
    "lastName": "Doe",
    "streetAddress": "123 Main St",
    "extendedAddress": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zipCode": "10001",
    "iso2": "US"
  },
  "deviceFingerprint": {
    "sessionId": "session_123",
    "hostname": "localhost",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0..."
  }
}
```

**Request Body (Vault Payment):**
```json
{
  "amount": "100.00",
  "currency": "USD",
  "customerDetail": {
    "id": "customer_123",
    "email": "<EMAIL>"
  },
  "vaultDetails": {
    "paymentInstrumentId": "instrument_123"
  }
}
```

**Response:**
```json
{
  "paymentId": "payment_123456789",
  "amount": "100.00",
  "currency": "USD",
  "status": "AUTHORIZED",
  "authorizationId": "auth_123456789",
  "processorTransactionId": "proc_123456789",
  "createdAt": "2024-01-15T10:30:00Z"
}
```

**Status Codes:**
- `200 OK`: Payment authorized successfully
- `400 Bad Request`: Invalid request data or payment declined
- `403 Forbidden`: Payment not authorized for customer
- `404 Not Found`: Payment session not found
- `409 Conflict`: Payment already authorized
- `500 Internal Server Error`: Server error

#### Capture Payment
Captures a previously authorized payment.

**Endpoint:** `POST /payments/{id}/capture`

**Request Body:**
```json
{
  "paymentId": "payment_123456789"
}
```

**Response:**
```json
{
  "paymentId": "payment_123456789",
  "amount": "100.00",
  "currency": "USD",
  "status": "CAPTURED",
  "captureId": "capture_123456789",
  "processorTransactionId": "proc_123456789",
  "createdAt": "2024-01-15T10:35:00Z"
}
```

**Status Codes:**
- `200 OK`: Payment captured successfully
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Payment not found
- `409 Conflict`: Payment already captured or not in correct state
- `500 Internal Server Error`: Server error

#### Reverse Payment
Reverses a previously authorized payment.

**Endpoint:** `POST /payments/{id}/reverse`

**Request Body:**
```json
{
  "paymentId": "payment_123456789",
  "reason": "Customer requested cancellation"
}
```

**Response:**
```json
{
  "paymentId": "payment_123456789",
  "amount": "100.00",
  "currency": "USD",
  "status": "REVERSED",
  "reversalId": "reversal_123456789",
  "processorTransactionId": "proc_123456789",
  "createdAt": "2024-01-15T10:40:00Z"
}
```

**Status Codes:**
- `200 OK`: Payment reversed successfully
- `400 Bad Request`: Invalid request data or missing reason
- `404 Not Found`: Payment not found
- `409 Conflict`: Payment already reversed or not in correct state
- `500 Internal Server Error`: Server error

#### Void Payment
Voids a previously captured payment.

**Endpoint:** `POST /payments/{id}/void`

**Request Body:**
```json
{
  "paymentId": "payment_123456789"
}
```

**Response:**
```json
{
  "paymentId": "payment_123456789",
  "amount": "100.00",
  "currency": "USD",
  "status": "VOIDED",
  "voidId": "void_123456789",
  "processorTransactionId": "proc_123456789",
  "createdAt": "2024-01-15T10:45:00Z"
}
```

**Status Codes:**
- `200 OK`: Payment voided successfully
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Payment not found
- `409 Conflict`: Payment already voided or not in correct state
- `500 Internal Server Error`: Server error

### Customer Management

#### Get Customer
Retrieves customer information including payment instruments.

**Endpoint:** `GET /customers/{id}`

**Response:**
```json
{
  "id": "customer_123",
  "email": "<EMAIL>",
  "status": "OPEN",
  "billingAddress": {
    "firstName": "John",
    "lastName": "Doe",
    "streetAddress": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zipCode": "10001",
    "iso2": "US"
  },
  "paymentInstruments": [
    {
      "id": "instrument_123",
      "type": "CREDIT_CARD",
      "cardType": "VISA",
      "maskedNumber": "****1234",
      "expiryMonth": "12",
      "expiryYear": "2025",
      "isDefault": true,
      "billingAddress": {
        "firstName": "John",
        "lastName": "Doe",
        "streetAddress": "123 Main St",
        "city": "New York",
        "state": "NY",
        "zipCode": "10001",
        "iso2": "US"
      }
    }
  ]
}
```

**Status Codes:**
- `200 OK`: Customer found
- `403 Forbidden`: Customer account is locked
- `404 Not Found`: Customer not found
- `500 Internal Server Error`: Server error

#### Delete Customer
Deletes a customer and all associated data.

**Endpoint:** `DELETE /customers/{id}`

**Response:** No content

**Status Codes:**
- `204 No Content`: Customer deleted successfully
- `403 Forbidden`: Customer account is locked
- `404 Not Found`: Customer not found
- `409 Conflict`: Duplicate transaction
- `500 Internal Server Error`: Server error

#### Update Payment Instrument
Updates the billing address of a payment instrument.

**Endpoint:** `PUT /customers/{id}/payment-instruments/{instrumentId}`

**Request Body:**
```json
{
  "billingAddress": {
    "firstName": "John",
    "lastName": "Doe",
    "streetAddress": "456 Oak Ave",
    "city": "Los Angeles",
    "state": "CA",
    "zipCode": "90210",
    "iso2": "US"
  }
}
```

**Response:**
```json
[
  {
    "id": "instrument_123",
    "type": "CREDIT_CARD",
    "cardType": "VISA",
    "maskedNumber": "****1234",
    "expiryMonth": "12",
    "expiryYear": "2025",
    "isDefault": true,
    "billingAddress": {
      "firstName": "John",
      "lastName": "Doe",
      "streetAddress": "456 Oak Ave",
      "city": "Los Angeles",
      "state": "CA",
      "zipCode": "90210",
      "iso2": "US"
    }
  }
]
```

**Status Codes:**
- `200 OK`: Payment instrument updated
- `400 Bad Request`: Invalid request data
- `403 Forbidden`: Customer account is locked
- `404 Not Found`: Payment instrument not found
- `409 Conflict`: Duplicate transaction
- `500 Internal Server Error`: Server error

#### Update Default Payment Instrument
Sets or unsets a payment instrument as the default.

**Endpoint:** `PATCH /customers/{id}/payment-instruments/{instrumentId}`

**Request Body:**
```json
{
  "isDefault": true
}
```

**Response:** Array of payment instruments (same format as above)

**Status Codes:**
- `200 OK`: Default payment instrument updated
- `400 Bad Request`: Invalid request data
- `403 Forbidden`: Customer account is locked
- `404 Not Found`: Payment instrument not found
- `409 Conflict`: Duplicate transaction
- `500 Internal Server Error`: Server error

#### Delete Payment Instrument
Deletes a payment instrument from a customer's vault.

**Endpoint:** `DELETE /customers/{id}/payment-instruments/{instrumentId}`

**Response:** No content

**Status Codes:**
- `204 No Content`: Payment instrument deleted
- `400 Bad Request`: Invalid request data
- `403 Forbidden`: Customer account is locked
- `404 Not Found`: Payment instrument not found
- `409 Conflict`: Cannot delete default payment instrument
- `500 Internal Server Error`: Server error

### Payment Inquiry

#### Get All Payments
Retrieves all payments with their events.

**Endpoint:** `GET /payments`

**Response:**
```json
[
  {
    "paymentId": "payment_123456789",
    "customerId": "customer_123",
    "amount": "100.00",
    "currency": "USD",
    "status": "CAPTURED",
    "canonicalName": "John Doe",
    "paymentMethod": "CREDIT_CARD",
    "cardType": "VISA",
    "createdAt": "2024-01-15T10:30:00Z",
    "events": [
      {
        "referenceId": "auth_123456789",
        "paymentEventStatus": "SUCCESS",
        "paymentEventType": "AUTHORIZATION",
        "amount": "100.00",
        "currency": "USD",
        "errorCode": null,
        "errorMessage": null,
        "metadata": {},
        "createdAt": "2024-01-15T10:30:00Z"
      },
      {
        "referenceId": "capture_123456789",
        "paymentEventStatus": "SUCCESS",
        "paymentEventType": "CAPTURE",
        "amount": "100.00",
        "currency": "USD",
        "errorCode": null,
        "errorMessage": null,
        "metadata": {},
        "createdAt": "2024-01-15T10:35:00Z"
      }
    ]
  }
]
```

**Status Codes:**
- `200 OK`: Payments retrieved successfully
- `500 Internal Server Error`: Server error

#### Get Payment by ID
Retrieves a specific payment with its events.

**Endpoint:** `GET /payments/{id}`

**Response:** Same format as individual payment object above

**Status Codes:**
- `200 OK`: Payment found
- `404 Not Found`: Payment not found
- `500 Internal Server Error`: Server error

## Request/Response Examples

### Complete Payment Flow Example

#### 1. Create Payment Session
```bash
curl -X POST http://localhost:8080/api/v1/payment-sessions \
  -H "Content-Type: application/json" \
  -d '{
    "paymentMethod": "CREDIT_CARD",
    "deviceType": "WEB"
  }'
```

#### 2. Authorize Payment
```bash
curl -X POST http://localhost:8080/api/v1/payments/{paymentId}/authorize \
  -H "Content-Type: application/json" \
  -d '{
    "amount": "100.00",
    "currency": "USD",
    "customerDetail": {
      "id": "customer_123",
      "email": "<EMAIL>",
      "phone": "+1234567890"
    },
    "creditCardDetail": {
      "paymentData": "jwt_token_from_cybersource_flex",
      "storeInVault": true
    },
    "billingAddress": {
      "firstName": "John",
      "lastName": "Doe",
      "streetAddress": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "iso2": "US"
    },
    "deviceFingerprint": {
      "sessionId": "session_123",
      "ipAddress": "***********",
      "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
  }'
```

#### 3. Capture Payment
```bash
curl -X POST http://localhost:8080/api/v1/payments/{paymentId}/capture \
  -H "Content-Type: application/json" \
  -d '{
    "paymentId": "payment_123456789"
  }'
```

### Vault Payment Example

#### 1. Create Vault Payment Session
```bash
curl -X POST http://localhost:8080/api/v1/payment-sessions \
  -H "Content-Type: application/json" \
  -d '{
    "paymentMethod": "VAULT",
    "deviceType": "WEB"
  }'
```

#### 2. Authorize with Stored Payment Instrument
```bash
curl -X POST http://localhost:8080/api/v1/payments/{paymentId}/authorize \
  -H "Content-Type: application/json" \
  -d '{
    "amount": "50.00",
    "currency": "USD",
    "customerDetail": {
      "id": "customer_123",
      "email": "<EMAIL>"
    },
    "vaultDetails": {
      "paymentInstrumentId": "instrument_123"
    }
  }'
```

### Customer Management Examples

#### Get Customer Information
```bash
curl -X GET http://localhost:8080/api/v1/customers/customer_123 \
  -H "Content-Type: application/json"
```

#### Update Payment Instrument Billing Address
```bash
curl -X PUT http://localhost:8080/api/v1/customers/customer_123/payment-instruments/instrument_123 \
  -H "Content-Type: application/json" \
  -d '{
    "billingAddress": {
      "firstName": "John",
      "lastName": "Doe",
      "streetAddress": "456 Oak Ave",
      "city": "Los Angeles",
      "state": "CA",
      "zipCode": "90210",
      "iso2": "US"
    }
  }'
```

#### Set Default Payment Instrument
```bash
curl -X PATCH http://localhost:8080/api/v1/customers/customer_123/payment-instruments/instrument_123 \
  -H "Content-Type: application/json" \
  -d '{
    "isDefault": true
  }'
```

## Error Handling

The API uses standard HTTP status codes and returns detailed error information in JSON format.

### Error Response Format
```json
{
  "error": "INVALID_REQUEST",
  "message": "Credit card details are required for credit card payments",
  "details": {
    "field": "creditCardDetail",
    "code": "MISSING_REQUIRED_FIELD"
  }
}
```

### Common Error Codes

| HTTP Status | Error Code | Description |
|-------------|------------|-------------|
| 400 | `INVALID_REQUEST` | Request validation failed |
| 400 | `PAYMENT_DECLINED` | Payment was declined by processor |
| 400 | `INVALID_CARD` | Credit card information is invalid |
| 400 | `MISSING_REQUIRED_FIELD` | Required field is missing |
| 401 | `UNAUTHORIZED` | Authentication failed |
| 403 | `FORBIDDEN` | Customer account is locked |
| 404 | `NOT_FOUND` | Resource not found |
| 404 | `CUSTOMER_NOT_FOUND` | Customer not found |
| 404 | `PAYMENT_NOT_FOUND` | Payment not found |
| 409 | `CONFLICT` | Resource already exists or invalid state |
| 409 | `DUPLICATE_TRANSACTION` | Transaction already processed |
| 500 | `INTERNAL_SERVER_ERROR` | Unexpected server error |

### Payment Status Values

| Status | Description |
|--------|-------------|
| `PENDING` | Payment is pending processing |
| `AUTHORIZED` | Payment has been authorized |
| `CAPTURED` | Payment has been captured |
| `REVERSED` | Payment authorization has been reversed |
| `VOIDED` | Payment capture has been voided |
| `FAILED` | Payment processing failed |
| `DECLINED` | Payment was declined |

### Payment Event Types

| Event Type | Description |
|------------|-------------|
| `AUTHORIZATION` | Payment authorization event |
| `CAPTURE` | Payment capture event |
| `REVERSAL` | Payment reversal event |
| `VOID` | Payment void event |

## Testing

### Testing Your Integration

1. **Create a Payment Session**: Start by creating a payment session for your desired payment method
2. **Authorize Payment**: Use the payment session ID to authorize a payment
3. **Capture Payment**: Capture the authorized payment to complete the transaction
4. **Test Error Scenarios**: Try invalid requests to test error handling
5. **Test Customer Management**: Create, retrieve, update, and delete customers and payment instruments

### Test Credit Card Numbers (Sandbox)

Use these test credit card numbers in the Cybersource sandbox environment:

| Card Type | Number | CVV | Expiry |
|-----------|--------|-----|--------|
| Visa | **************** | 123 | 12/2025 |
| Mastercard | **************** | 123 | 12/2025 |
| American Express | *************** | 1234 | 12/2025 |

### Testing Checklist

- [ ] Create payment session for each payment method
- [ ] Authorize credit card payment with tokenization
- [ ] Authorize vault payment with stored instrument
- [ ] Authorize default vault payment
- [ ] Capture authorized payment
- [ ] Reverse authorized payment
- [ ] Void captured payment
- [ ] Retrieve customer information
- [ ] Update payment instrument billing address
- [ ] Set/unset default payment instrument
- [ ] Delete payment instrument
- [ ] Delete customer
- [ ] Query payment history
- [ ] Test error scenarios (invalid data, not found, conflicts)

## DEPLOYMENT

### Build and Deploy
```shell
./gradlew shadowJar
docker build -t purchase-api:0.0.6 -f docs/Dockerfile .
docker tag purchase-api:0.0.6 584252333092.dkr.ecr.us-west-2.amazonaws.com/purchase-api:0.0.6
docker push 584252333092.dkr.ecr.us-west-2.amazonaws.com/purchase-api:0.0.6
kubectl apply -f docs/deployment-dev.yaml
```

### Check update available
`./gradlew versionCatalogUpdate --interactive`

Apply update
`./gradlew versionCatalogUpdate`

`./gradlew openApiGenerate`

### Deploy to CodeArtifact
`echo "codeartifactToken=$CODEARTIFACT_AUTH_TOKEN" > ~/.gradle/gradle.properties`

`gradle publish`

```shell
publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            url 'https://esdiac-584252333092.d.codeartifact.us-west-2.amazonaws.com/maven/esdiac/'
            credentials {
                username "aws"
                password System.env.CODEARTIFACT_AUTH_TOKEN
            }
        }
    }
}
```

---

## API Summary

**Total Endpoints: 13**

### Payment Sessions (1 endpoint)
- `POST /payment-sessions` - Create payment session

### Payment Operations (4 endpoints)
- `POST /payments/{id}/authorize` - Authorize payment
- `POST /payments/{id}/capture` - Capture payment
- `POST /payments/{id}/reverse` - Reverse payment
- `POST /payments/{id}/void` - Void payment

### Customer Management (5 endpoints)
- `GET /customers/{id}` - Get customer
- `DELETE /customers/{id}` - Delete customer
- `PUT /customers/{id}/payment-instruments/{instrumentId}` - Update payment instrument
- `PATCH /customers/{id}/payment-instruments/{instrumentId}` - Update default payment instrument
- `DELETE /customers/{id}/payment-instruments/{instrumentId}` - Delete payment instrument

### Payment Inquiry (2 endpoints)
- `GET /payments` - Get all payments
- `GET /payments/{id}` - Get payment by ID

### Health Check (1 endpoint)
- `GET /healthz` - Health check endpoint

This API provides a complete payment processing solution with comprehensive customer and payment management capabilities.