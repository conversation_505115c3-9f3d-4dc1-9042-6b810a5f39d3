# Purchase API - Cybersource Payment Integration

A comprehensive payment processing API built with Kotlin and Ktor, integrating with Cybersource for secure payment processing, customer management, and payment instrument storage.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Getting Started](#getting-started)
- [API Documentation](#api-documentation)
- [Authentication](#authentication)
- [Error Handling](#error-handling)
- [Testing](#testing)
- [Deployment](#deployment)

## Overview

The Purchase API provides a complete payment processing solution with the following capabilities:
- **Customer Management**: Create, retrieve, update, and delete customers
- **Payment Instruments**: Manage stored payment methods (credit cards) with vault functionality
- **Payment Processing**: Authorize, capture, reverse, and void payments
- **Session Management**: Secure payment session creation with device fingerprinting
- **Multi-Payment Methods**: Support for credit cards, vault payments, and future support for PayPal, Google Pay, Apple Pay

## Features

### Core Payment Operations
- ✅ **Payment Authorization**: Secure credit card authorization with fraud detection
- ✅ **Payment Capture**: Capture authorized payments
- ✅ **Payment Reversal**: Reverse authorized payments
- ✅ **Payment Void**: Void captured payments
- ✅ **Payment Sessions**: Secure session management with JWT tokens

### Customer & Vault Management
- ✅ **Customer Creation**: Create and manage customer profiles
- ✅ **Payment Instruments**: Store and manage payment methods securely
- ✅ **Default Payment Methods**: Set and update default payment instruments
- ✅ **Billing Address Management**: Update billing information for stored cards

### Security & Compliance
- ✅ **PCI Compliance**: Secure token-based payment processing
- ✅ **Device Fingerprinting**: Advanced fraud detection
- ✅ **JWT Token Security**: Secure payment data transmission
- ✅ **Address Verification**: AVS and CVV verification

## Getting Started

### Prerequisites
- Java 11 or higher
- Gradle 7.0+
- Cybersource merchant account with API credentials
- PostgreSQL database

### Configuration
1. Set up your Cybersource credentials in the configuration
2. Configure database connection
3. Set up environment variables for API keys

### Running the Application
```bash
./gradlew run
```

The API will be available at `http://localhost:8080`

### API Documentation
Interactive API documentation is available at:
- Swagger UI: `http://localhost:8080/swagger`
- OpenAPI Spec: `http://localhost:8080/openapi.yaml`

## API Documentation

### Base URL
```
Production: http://purchase-api
Staging: http://purchase-api-
Local: http://localhost:8080
```

All API endpoints are prefixed with `/api/v1`

### Payment Flow Overview

1. **Create Payment Session** → Get secure payment token
2. **Authorize Payment** → Authorize payment with card/vault
3. **Capture Payment** → Capture authorized funds
4. **Optional: Reverse/Void** → Cancel or reverse payment

---

## Endpoints

### 1. Payment Sessions

#### Create Payment Session
Creates a secure payment session for processing payments.

**Endpoint:** `POST /api/v1/payment-sessions`

**Request Body:**
```json
{
  "paymentMethod": "CREDIT_CARD",
  "deviceType": "WEB"
}
```

**Payment Methods:**
- `CREDIT_CARD` - New credit card payment
- `VAULT` - Use stored payment instrument
- `DEFAULT_VAULT` - Use customer's default payment instrument
- `PAYPAL` - PayPal payment (coming soon)
- `GOOGLE_PAY` - Google Pay (coming soon)
- `APPLE_PAY` - Apple Pay (coming soon)

**Device Types:**
- `WEB` - Web browser
- `ANDROID` - Android mobile app
- `IOS` - iOS mobile app

**Response:**
```json
{
  "paymentId": "550e8400-e29b-41d4-a716-446655440000",
  "paymentSignature": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "deviceType": "WEB",
  "paymentMethod": "CREDIT_CARD",
  "organizationId": "your-org-id",
  "merchantId": "your-merchant-id"
}
```

---

### 2. Payment Operations

#### Authorize Payment
Authorizes a payment using credit card or stored payment instrument.

**Endpoint:** `POST /api/v1/payments/{paymentId}/authorize`

**Path Parameters:**
- `paymentId` (string, required) - Payment session ID from payment session creation

**Request Body:**
```json
{
  "amount": "100.00",
  "currency": "USD",
  "customerDetail": {
    "id": "customer-123",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "firstName": "John",
    "lastName": "Doe"
  },
  "creditCardDetail": {
    "paymentData": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "storeInVault": true
  },
  "billingAddress": {
    "firstName": "John",
    "lastName": "Doe",
    "iso2": "US",
    "state": "CA",
    "city": "San Francisco",
    "zipCode": "94105",
    "streetAddress": "123 Main St",
    "extendedAddress": "Apt 4B"
  },
  "deviceFingerprint": {
    "sessionId": "device-session-123",
    "ipAddress": "***********",
    "hostname": "customer-device.local",
    "userAgent": "Mozilla/5.0..."
  }
}
```

**For Vault Payments:**
```json
{
  "amount": "100.00",
  "currency": "USD",
  "customerDetail": { ... },
  "vaultDetails": {
    "paymentInstrumentId": "payment-instrument-123"
  },
  "deviceFingerprint": { ... }
}
```

**Response:**
```json
{
  "date": "2024-01-15T10:30:00Z",
  "paymentId": "550e8400-e29b-41d4-a716-446655440000",
  "amount": "100.00",
  "currency": "USD",
  "canonicalName": "****************",
  "cardType": "Visa"
}
```

#### Capture Payment
Captures an authorized payment to complete the transaction.

**Endpoint:** `POST /api/v1/payments/{paymentId}/capture`

**Request Body:**
```json
{
  "paymentId": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Response:**
```json
{
  "date": "2024-01-15T10:35:00Z",
  "paymentId": "550e8400-e29b-41d4-a716-446655440000",
  "amount": "100.00",
  "currency": "USD",
  "canonicalName": "****************",
  "cardType": "Visa"
}
```

#### Reverse Payment
Reverses an authorized payment before capture.

**Endpoint:** `POST /api/v1/payments/{paymentId}/reverse`

**Request Body:**
```json
{
  "paymentId": "550e8400-e29b-41d4-a716-446655440000",
  "reason": "Customer requested cancellation"
}
```

**Response:**
```json
{
  "date": "2024-01-15T10:40:00Z",
  "paymentId": "550e8400-e29b-41d4-a716-446655440000",
  "amount": "100.00",
  "currency": "USD"
}
```

#### Void Payment
Voids a captured payment.

**Endpoint:** `POST /api/v1/payments/{paymentId}/void`

**Request Body:**
```json
{
  "paymentId": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Response:**
```json
{
  "date": "2024-01-15T10:45:00Z",
  "paymentId": "550e8400-e29b-41d4-a716-446655440000",
  "amount": "100.00",
  "currency": "USD"
}
```

---

### 3. Payment Inquiry

#### Get All Payments
Retrieves all payments with their events.

**Endpoint:** `GET /api/v1/payments`

**Response:**
```json
[
  {
    "paymentId": "550e8400-e29b-41d4-a716-446655440000",
    "customerId": "customer-123",
    "amount": "100.00",
    "currency": "USD",
    "status": "CAPTURED",
    "paymentMethod": "CREDIT_CARD",
    "createdAt": "2024-01-15T10:30:00Z",
    "canonicalName": "****************",
    "cardType": "Visa",
    "events": [
      {
        "referenceId": "auth-ref-123",
        "paymentEventType": "AUTHORIZATION",
        "paymentEventStatus": "SUCCESS",
        "amount": "100.00",
        "currency": "USD",
        "createdAt": "2024-01-15T10:30:00Z",
        "errorCode": null,
        "errorMessage": null,
        "metadata": null
      }
    ]
  }
]
```

#### Get Payment by ID
Retrieves a specific payment with its events.

**Endpoint:** `GET /api/v1/payments/{paymentId}`

**Response:** Same structure as individual payment in the array above.

---

### 4. Customer Management

#### Get Customer
Retrieves customer information including payment instruments.

**Endpoint:** `GET /api/v1/customers/{customerId}`

**Path Parameters:**
- `customerId` (string, required) - Customer ID

**Response:**
```json
{
  "id": "customer-123",
  "email": "<EMAIL>",
  "billingAddress": {
    "iso2": "US",
    "state": "CA",
    "city": "San Francisco",
    "zipCode": "94105",
    "streetAddress": "123 Main St",
    "extendedAddress": "Apt 4B"
  },
  "paymentInstruments": [
    {
      "id": "payment-instrument-123",
      "paymentMethod": "CREDIT_CARD",
      "canonicalName": "****************",
      "brand": "Visa",
      "last4": "1111",
      "isDefault": true
    }
  ]
}
```

#### Delete Customer
Deletes a customer and all associated payment instruments.

**Endpoint:** `DELETE /api/v1/customers/{customerId}`

**Response:** `204 No Content`

---

### 5. Payment Instrument Management

#### Update Payment Instrument
Updates billing address for a payment instrument.

**Endpoint:** `PUT /api/v1/customers/{customerId}/payment-instruments/{paymentInstrumentId}`

**Request Body:**
```json
{
  "billingAddress": {
    "firstName": "John",
    "lastName": "Doe",
    "iso2": "US",
    "state": "CA",
    "city": "San Francisco",
    "zipCode": "94105",
    "streetAddress": "123 Main St",
    "extendedAddress": "Apt 4B"
  }
}
```

**Response:**
```json
[
  {
    "id": "payment-instrument-123",
    "paymentMethod": "CREDIT_CARD",
    "canonicalName": "****************",
    "brand": "Visa",
    "last4": "1111",
    "isDefault": true
  }
]
```

#### Set Default Payment Instrument
Sets a payment instrument as the default for a customer.

**Endpoint:** `PATCH /api/v1/customers/{customerId}/payment-instruments/{paymentInstrumentId}`

**Request Body:**
```json
{
  "isDefault": true
}
```

**Response:** Array of payment instruments (same as above)

#### Delete Payment Instrument
Deletes a payment instrument from a customer's vault.

**Endpoint:** `DELETE /api/v1/customers/{customerId}/payment-instruments/{paymentInstrumentId}`

**Response:** `204 No Content`

**Note:** Cannot delete the default payment instrument. Set another instrument as default first.

---

## Authentication

Currently, the API does not require authentication for testing purposes. In production, implement appropriate authentication mechanisms such as:
- API Keys
- JWT Tokens
- OAuth 2.0

## Error Handling

### HTTP Status Codes
- `200 OK` - Request successful
- `204 No Content` - Request successful, no content returned
- `400 Bad Request` - Invalid request data
- `403 Forbidden` - Access denied
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict (e.g., duplicate transaction)
- `500 Internal Server Error` - Server error

### Error Response Format
```json
{
  "error": "INVALID_REQUEST",
  "message": "Card number is required"
}
```

### Common Error Codes

#### Payment Errors
- `INVALID_CARD` - Invalid card number or expired card
- `CARD_TYPE_NOT_ACCEPTED` - Card type not supported
- `INSUFFICIENT_FUNDS` - Insufficient funds on card
- `DUPLICATE_REQUEST` - Transaction already processed
- `INVALID_AMOUNT` - Invalid payment amount
- `PAYMENT_DECLINED` - Payment declined by issuer

#### Customer Errors
- `CUSTOMER_NOT_FOUND` - Customer does not exist
- `PAYMENT_INSTRUMENT_NOT_FOUND` - Payment instrument not found
- `CANNOT_DELETE_DEFAULT_INSTRUMENT` - Cannot delete default payment method

#### Validation Errors
- `MISSING_FIELD` - Required field missing
- `INVALID_DATA` - Invalid field format or value
- `INVALID_CURRENCY` - Unsupported currency
- `INVALID_PAYMENT_METHOD` - Unsupported payment method

---

## Testing

### Test Credit Cards
Use these test credit card numbers for testing:

**Visa:**
- `****************` - Success
- `****************` - Declined

**Mastercard:**
- `****************` - Success
- `5000000000000009` - Declined

**American Express:**
- `***************` - Success
- `***************` - Declined

**Test Data:**
- Expiration: Any future date (e.g., `12/2025`)
- CVV: Any 3-4 digit number (e.g., `123`)
- ZIP: Any valid ZIP code (e.g., `94105`)

### Running Tests
```bash
# Run all tests
./gradlew test

# Run unit tests only
./gradlew test --tests "*Unit*"

# Run integration tests only
./gradlew test --tests "*Integration*"
```

### Test Coverage
```bash
./gradlew jacocoTestReport
```
View coverage report at `build/reports/jacoco/test/html/index.html`

---

## Example Usage

### Complete Payment Flow

#### 1. Create Payment Session
```bash
curl -X POST http://localhost:8080/api/v1/payment-sessions \
  -H "Content-Type: application/json" \
  -d '{
    "paymentMethod": "CREDIT_CARD",
    "deviceType": "WEB"
  }'
```

#### 2. Authorize Payment
```bash
curl -X POST http://localhost:8080/api/v1/payments/{paymentId}/authorize \
  -H "Content-Type: application/json" \
  -d '{
    "amount": "100.00",
    "currency": "USD",
    "customerDetail": {
      "id": "customer-123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe"
    },
    "creditCardDetail": {
      "paymentData": "jwt-token-from-frontend",
      "storeInVault": true
    },
    "billingAddress": {
      "firstName": "John",
      "lastName": "Doe",
      "iso2": "US",
      "state": "CA",
      "city": "San Francisco",
      "zipCode": "94105",
      "streetAddress": "123 Main St",
      "extendedAddress": ""
    }
  }'
```

#### 3. Capture Payment
```bash
curl -X POST http://localhost:8080/api/v1/payments/{paymentId}/capture \
  -H "Content-Type: application/json" \
  -d '{
    "paymentId": "550e8400-e29b-41d4-a716-446655440000"
  }'
```

### Vault Payment Flow

#### 1. Get Customer Payment Instruments
```bash
curl -X GET http://localhost:8080/api/v1/customers/customer-123
```

#### 2. Authorize with Stored Card
```bash
curl -X POST http://localhost:8080/api/v1/payments/{paymentId}/authorize \
  -H "Content-Type: application/json" \
  -d '{
    "amount": "50.00",
    "currency": "USD",
    "customerDetail": {
      "id": "customer-123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe"
    },
    "vaultDetails": {
      "paymentInstrumentId": "payment-instrument-123"
    }
  }'
```

---

## Deployment

### Build Application
```shell
./gradlew shadowJar
```

### Docker Deployment
```shell
# Build Docker image
docker build -t purchase-api:0.0.6 -f docs/Dockerfile .

# Tag for ECR
docker tag purchase-api:0.0.6 58**********.dkr.ecr.us-west-2.amazonaws.com/purchase-api:0.0.6

# Push to ECR
docker push 58**********.dkr.ecr.us-west-2.amazonaws.com/purchase-api:0.0.6

# Deploy to Kubernetes
kubectl apply -f docs/deployment-dev.yaml
```

### Environment Variables
```bash
# Cybersource Configuration
CYBERSOURCE_MERCHANT_ID=your-merchant-id
CYBERSOURCE_API_KEY=your-api-key
CYBERSOURCE_SECRET_KEY=your-secret-key
CYBERSOURCE_ENVIRONMENT=sandbox  # or production

# Database Configuration
DATABASE_URL=********************************************
DATABASE_USER=your-db-user
DATABASE_PASSWORD=your-db-password
```

### Health Check
```bash
curl http://localhost:8080/health
```

---

## Development

### Check for Updates
```bash
./gradlew versionCatalogUpdate --interactive
```

### Apply Updates
```bash
./gradlew versionCatalogUpdate
```

### Generate OpenAPI Spec
```bash
./gradlew openApiGenerate
```

### Deploy to CodeArtifact
```bash
echo "codeartifactToken=$CODEARTIFACT_AUTH_TOKEN" > ~/.gradle/gradle.properties
gradle publish
```

---

## Support

For questions or issues, please contact:
- Email: <EMAIL>
- Documentation: Available at `/swagger` endpoint when running

## License

Copyright © 2024 Esdiac. All rights reserved.