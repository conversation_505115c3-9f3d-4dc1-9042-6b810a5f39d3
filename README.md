## DEPLOYMENT
```shell
./gradlew shadowJar
docker build -t purchase-api:0.0.6 -f docs/Dockerfile .
docker tag purchase-api:0.0.6 584252333092.dkr.ecr.us-west-2.amazonaws.com/purchase-api:0.0.6
docker push 584252333092.dkr.ecr.us-west-2.amazonaws.com/purchase-api:0.0.6
kubectl apply -f docs/deployment-dev.yaml
```

## Check update available
`./gradlew versionCatalogUpdate --interactive`

Apply update
`./gradlew versionCatalogUpdate`

`./gradlew openApiGenerate`

## Deploy to CodeArtifact
`echo "codeartifactToken=$CODEARTIFACT_AUTH_TOKEN" > ~/.gradle/gradle.properties`

`gradle publish`

```shell
publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            url 'https://esdiac-584252333092.d.codeartifact.us-west-2.amazonaws.com/maven/esdiac/'
            credentials {
                username "aws"
                password System.env.CODEARTIFACT_AUTH_TOKEN
            }
        }
    }
}
```