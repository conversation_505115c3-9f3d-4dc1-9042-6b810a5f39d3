# Testing Guide

This document provides comprehensive testing setup and guidelines for the Purchase API, following official Kotlin and <PERSON><PERSON> testing practices.

## 🔧 **Current Status: FIXED COMPILATION ISSUES**

### **Issues Resolved:**
1. ✅ **Fixed Ktor test dependency** - Changed from `ktorLibs.server.test.host` to direct dependency
2. ✅ **Fixed module import** - Added `import module` to all test files
3. ✅ **Fixed assertion conflicts** - Used `kotlin.test.assertNotNull` instead of JUnit's `assertNotNull`
4. ✅ **Fixed Payment model** - Added all required parameters with correct types
5. ✅ **Fixed Instant import** - Added `kotlin.time.Instant` import

### **Ready to Test:**
```bash
# Test simple functionality first
./gradlew test --tests "SimpleTest"

# Test application startup
./gradlew test --tests "ApplicationTest"

# Run all tests
./gradlew test
```

## Testing Framework Setup

### Dependencies Added
- **JUnit 5**: Modern testing framework for Java/Kotlin
- **MockK**: Mocking library for Kotlin
- **Ktor Test Host**: Official Ktor testing utilities
- **Kotlinx Coroutines Test**: Testing utilities for coroutines
- **H2 Database**: In-memory database for testing
- **TestContainers**: Integration testing with real databases

### Build Configuration
```kotlin
// build.gradle.kts
testImplementation(ktorLibs.server.test.host)
testImplementation(libs.junit.jupiter.api)
testImplementation(libs.junit.jupiter.engine)
testImplementation(libs.junit.jupiter.params)
testImplementation(libs.mockk)
testImplementation(libs.kotlin.test.junit5)
testImplementation(libs.kotlinx.coroutines.test)
testImplementation(libs.h2.database)
testImplementation(libs.testcontainers.junit.jupiter)
testImplementation(libs.testcontainers.postgresql)

tasks.test {
    useJUnitPlatform()
}
```

## Test Structure

### 1. Unit Tests (`src/test/kotlin/services/`)
- **CybersourceServiceTest.kt**: Tests business logic in isolation
- Uses MockK for mocking dependencies
- Follows JUnit 5 nested test structure
- Tests payment session generation, validation, error handling

### 2. Integration Tests (`src/test/kotlin/routes/`)
- **PaymentSessionRoutesTest.kt**: Tests payment session endpoints
- **PaymentRoutesTest.kt**: Tests payment operation endpoints  
- **CustomerRoutesTest.kt**: Tests customer management endpoints
- Uses Ktor's `testApplication` for full HTTP testing
- Tests complete request/response flow

### 3. Application Tests (`src/test/kotlin/`)
- **ApplicationTest.kt**: Basic application startup and health checks

## Running Tests

### Command Line
```bash
# Run all tests
./gradlew test

# Run specific test class
./gradlew test --tests "CybersourceServiceTest"

# Run tests with detailed output
./gradlew test --info

# Run tests and generate reports
./gradlew test jacocoTestReport
```

### IDE Integration
- Tests are compatible with IntelliJ IDEA and other JUnit 5 supporting IDEs
- Right-click on test files or methods to run individual tests
- Use IDE test runner for debugging

## Test Categories

### Unit Tests (CybersourceServiceTest)

#### ✅ **Payment Session Tests**
- `testGenerateSessionSuccess()` - Validates session generation
- `testGenerateUniquePaymentIds()` - Ensures unique payment IDs
- `testDifferentDeviceTypes()` - Tests all device types (WEB, ANDROID, IOS)
- `testDifferentPaymentMethods()` - Tests payment methods (CREDIT_CARD, VAULT, DEFAULT_VAULT)

#### ✅ **Repository Integration Tests**
- `testAddPaymentCallsRepository()` - Verifies repository method calls
- `testFindPaymentCallsRepository()` - Tests payment retrieval

#### ✅ **Error Handling Tests**
- `testNullPaymentSessionHandling()` - Tests exception handling
- `testPaymentSessionParameterValidation()` - Validates input parameters

#### ✅ **Business Logic Tests**
- `testPaymentSessionState()` - Tests session state management
- `testPaymentIdFormat()` - Validates UUID format
- `testPaymentSessionRequiredFields()` - Ensures all required fields present

#### ✅ **Performance Tests**
- `testPaymentSessionGenerationPerformance()` - Tests response time

### Integration Tests

#### ✅ **Payment Session Routes (PaymentSessionRoutesTest)**
- `testCreateCreditCardPaymentSession()` - POST /payment-sessions with CREDIT_CARD
- `testCreateVaultPaymentSession()` - POST /payment-sessions with VAULT
- `testCreateDefaultVaultPaymentSession()` - POST /payment-sessions with DEFAULT_VAULT
- `testInvalidPaymentMethod()` - Tests validation errors
- `testInvalidDeviceType()` - Tests validation errors
- `testMissingPaymentMethod()` - Tests required field validation
- `testMissingDeviceType()` - Tests required field validation
- `testEmptyRequestBody()` - Tests empty request handling
- `testMalformedJson()` - Tests JSON parsing errors
- `testConcurrentRequests()` - Tests concurrent request handling
- `testUniquePaymentIds()` - Tests unique ID generation
- `testCaseSensitivePaymentMethods()` - Tests case sensitivity
- `testCaseSensitiveDeviceTypes()` - Tests case sensitivity

#### ✅ **Payment Routes (PaymentRoutesTest)**
- `testGetAllPayments()` - GET /payments
- `testGetAllPaymentsContentType()` - Tests JSON response
- `testGetNonExistentPayment()` - GET /payments/{id} with invalid ID
- `testGetPaymentWithEmptyId()` - Tests empty ID handling
- `testGetPaymentWithSpecialCharacters()` - Tests special character handling
- `testAuthorizeNonExistentPayment()` - POST /payments/{id}/authorize with invalid session
- `testAuthorizeWithInvalidRequest()` - Tests validation errors
- `testAuthorizeWithMalformedJson()` - Tests JSON parsing
- `testCaptureNonExistentPayment()` - POST /payments/{id}/capture with invalid payment
- `testCaptureWithInvalidRequest()` - Tests validation errors
- `testReverseNonExistentPayment()` - POST /payments/{id}/reverse with invalid payment
- `testReverseWithMissingReason()` - Tests required field validation
- `testVoidNonExistentPayment()` - POST /payments/{id}/void with invalid payment
- `testVoidWithInvalidRequest()` - Tests validation errors
- `testUnsupportedMethodsOnPayments()` - Tests HTTP method validation

#### ✅ **Customer Routes (CustomerRoutesTest)**
- `testGetNonExistentCustomer()` - GET /customers/{id} with invalid ID
- `testGetCustomerWithEmptyId()` - Tests empty ID handling
- `testGetCustomerWithSpecialCharacters()` - Tests special character handling
- `testGetCustomerWithLongId()` - Tests very long ID handling
- `testDeleteNonExistentCustomer()` - DELETE /customers/{id} with invalid ID
- `testDeleteCustomerWithEmptyId()` - Tests empty ID handling
- `testUpdatePaymentInstrumentNonExistentCustomer()` - PUT with invalid customer
- `testUpdatePaymentInstrumentInvalidAddress()` - Tests validation errors
- `testUpdatePaymentInstrumentMalformedJson()` - Tests JSON parsing
- `testUpdateDefaultPaymentInstrumentNonExistentCustomer()` - PATCH with invalid customer
- `testUpdateDefaultPaymentInstrumentInvalidFlag()` - Tests boolean validation
- `testUpdateDefaultPaymentInstrumentMissingField()` - Tests required fields
- `testDeletePaymentInstrumentNonExistentCustomer()` - DELETE with invalid customer
- `testDeleteNonExistentPaymentInstrument()` - DELETE with invalid instrument
- `testDeletePaymentInstrumentEmptyId()` - Tests empty ID handling
- `testUnsupportedMethodsOnCustomers()` - Tests HTTP method validation
- `testUnsupportedMethodsOnPaymentInstruments()` - Tests HTTP method validation
- `testPutRequiresJsonContentType()` - Tests content type validation
- `testPatchRequiresJsonContentType()` - Tests content type validation

## Test Configuration

### Test Environment (`src/test/resources/application-test.conf`)
- Uses H2 in-memory database for fast testing
- Mock Cybersource credentials for testing
- Localhost configuration for test environment

### Environment Variables for Testing
```bash
# Set these for integration testing
export CYBERSOURCE_MICRO_FORM_ORIGIN=http://localhost:8080
export CYBERSOURCE_MERCHANT_ID=test_merchant_id
export CYBERSOURCE_ORGANIZATION_ID=test_organization_id
export CYBERSOURCE_RUN_ENVIRONMENT=sandbox
```

## Test Results Summary

### ✅ **Working Tests (Expected to Pass)**
1. **Unit Tests**: All CybersourceService business logic tests
2. **Route Validation Tests**: All HTTP method, content type, and parameter validation tests
3. **Error Handling Tests**: All 404, 400, 405 error response tests
4. **JSON Parsing Tests**: All malformed JSON and validation tests

### ⚠️ **Tests That May Fail (Due to Missing Environment Setup)**
1. **Payment Session Creation**: May fail if `CYBERSOURCE_MICRO_FORM_ORIGIN` not set
2. **Cybersource Integration**: May fail without proper Cybersource credentials
3. **Database Operations**: May fail without proper database configuration

### 🔧 **Tests That Need Real Data**
1. **Customer Retrieval**: Requires existing customer data
2. **Payment Authorization**: Requires valid payment sessions
3. **Payment Operations**: Require authorized payments

## Best Practices Implemented

### 1. **JUnit 5 Features**
- `@Nested` classes for logical test grouping
- `@DisplayName` for readable test descriptions
- `@BeforeEach` and `@AfterEach` for setup/cleanup
- `assertAll()` for multiple assertions
- Parameterized tests where appropriate

### 2. **Ktor Testing Best Practices**
- Uses `testApplication` for integration tests
- Proper HTTP client configuration
- Content type validation
- Status code assertions

### 3. **MockK Best Practices**
- Clear mock setup in `@BeforeEach`
- Mock cleanup in `@AfterEach`
- Verification of mock interactions
- Proper mock behavior definition

### 4. **Test Organization**
- Separate unit and integration tests
- Logical grouping with nested classes
- Clear test naming conventions
- Comprehensive error scenario coverage

## Running Specific Test Scenarios

### Test Payment Flow
```bash
# Test payment session creation
./gradlew test --tests "PaymentSessionRoutesTest"

# Test payment operations
./gradlew test --tests "PaymentRoutesTest"

# Test customer management
./gradlew test --tests "CustomerRoutesTest"
```

### Test Business Logic
```bash
# Test service layer
./gradlew test --tests "CybersourceServiceTest"
```

### Test Application Startup
```bash
# Test basic application functionality
./gradlew test --tests "ApplicationTest"
```

## Continuous Integration

### GitHub Actions / CI Pipeline
```yaml
- name: Run Tests
  run: ./gradlew test

- name: Generate Test Report
  run: ./gradlew jacocoTestReport

- name: Upload Test Results
  uses: actions/upload-artifact@v2
  with:
    name: test-results
    path: build/reports/tests/
```

This comprehensive testing setup follows official Kotlin and Ktor guidelines while maintaining your existing code structure and providing thorough coverage of all API endpoints.
